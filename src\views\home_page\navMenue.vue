<template>
  <el-aside width="200px">
    <!-- default-active为默认激活第index个菜单 -->
    <el-menu
      default-active="1"
      class="nav-menu"
      router
      background-color="#1f2937"
      text-color="#8da7f5"
      active-text-color="#ffffff"
    >
      <!-- 添加了router之后，index变为网页地址导航 -->
      <!-- 无子集 -->
      <el-menu-item index="/首页">
        <i class="el-icon-s-home nav-icon"></i>
        <span slot="title">首页</span>
      </el-menu-item>
      <el-menu-item index="/ML_sklearn">
        <i class="el-icon-s-custom nav-icon"></i>
        <span slot="title">ML_sklearn</span>
      </el-menu-item>
      <el-menu-item index="/病人管理">
        <i class="el-icon-s-custom nav-icon"></i>
        <span slot="title">病人管理</span>
      </el-menu-item>

      <el-menu-item index="/体检报告分析">
        <i class="el-icon-takeaway-box nav-icon"></i>
        <span slot="title">体检报告分析</span>
      </el-menu-item>

      <el-menu-item index="/ChatAI">
        <i class="el-icon-s-custom nav-icon"></i>
        <span slot="title">ChatAI</span>
      </el-menu-item>
      <!-- 有子集 -->
      <el-submenu index="4">
        <template slot="title">
          <i class="el-icon-first-aid-kit nav-icon"></i>
          药品管理</template
        >
        <el-menu-item index="/药品信息">药品信息</el-menu-item>
        <el-menu-item index="/药品借阅情况">药品借阅情况</el-menu-item>
      </el-submenu>
    </el-menu>
  </el-aside>
</template>

<style scoped>
.nav-menu {
  height: 100vh;
  border-right: none;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.nav-menu:not(.el-menu--collapse) {
  width: 200px;
}

.el-menu-item {
  height: 56px;
  line-height: 56px;
  margin: 8px 0;
  border-radius: 8px;
  margin-right: 12px;
  margin-left: 12px;
  transition: all 0.3s ease;
}

.el-menu-item:hover {
  background: rgba(141, 167, 245, 0.1) !important;
  transform: translateX(5px);
}

.el-menu-item.is-active {
  background: linear-gradient(
    90deg,
    rgba(141, 167, 245, 0.2),
    rgba(141, 167, 245, 0.1)
  ) !important;
  border-left: 4px solid #8da7f5;
}

.nav-icon {
  font-size: 18px;
  margin-right: 8px;
  color: #8da7f5;
}

.el-submenu__title {
  height: 56px;
  line-height: 56px;
  margin: 8px 12px;
  border-radius: 8px;
}

.el-submenu__title:hover {
  background: rgba(141, 167, 245, 0.1) !important;
}

.el-submenu .el-menu-item {
  min-width: auto;
  margin-left: 24px;
  background: transparent !important;
}

.el-submenu .el-menu-item:hover {
  color: #ffffff !important;
  transform: translateX(5px);
}

/* 添加顶部标题样式 */
.el-aside::before {
  content: "体检报告系统";
  display: block;
  padding: 20px;
  font-size: 20px;
  font-weight: bold;
  color: #8da7f5;
  text-align: center;
  background: linear-gradient(
    180deg,
    rgba(31, 41, 55, 0.9),
    rgba(31, 41, 55, 0.8)
  );
  margin-bottom: 20px;
  letter-spacing: 2px;
}

/* 适配移动端 */
@media screen and (max-width: 768px) {
  .el-aside {
    width: 64px !important;
  }

  .nav-menu {
    width: 64px !important;
  }

  .el-menu-item span,
  .el-submenu__title span {
    display: none;
  }

  .nav-icon {
    margin-right: 0;
    font-size: 20px;
  }
}
</style>

<script>
// 设置初始状态
history.pushState({ page: 1 }, "", ""); // 这里可以根据需要设置状态对象和标题

// 监听浏览器的返回事件
window.addEventListener("popstate", function (event) {
  // 可以在这里执行你希望的操作，比如返回上一页
  window.history.back();
});

export default {
  data() {
    return {};
  },

  methods: {},
};
</script>
