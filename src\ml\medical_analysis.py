import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from sklearn.ensemble import RandomForestClassifier
import json

# 读取数据
def load_and_merge_data():
    # 读取各个JSON文件
    patient_data = pd.read_json('path/to/patient_data.json')
    examination_data = pd.read_json('path/to/examination_data.json')
    diagnosis_data = pd.read_json('path/to/diagnosis_data.json')
    
    # 基于就诊编码合并数据
    merged_data = pd.merge(patient_data, examination_data, on='就诊编码')
    merged_data = pd.merge(merged_data, diagnosis_data, on='就诊编码')
    
    return merged_data

def analyze_data():
    data = load_and_merge_data()
    
    # 1. 患者年龄分组与疾病关系分析
    age_disease_correlation = data.groupby(['年龄段', '疾病类型']).size().reset_index()
    
    # 2. 使用K-means聚类分析患者特征
    features_for_clustering = ['年龄', '检查值', '其他数值特征']
    scaler = StandardScaler()
    scaled_features = scaler.fit_transform(data[features_for_clustering])
    
    kmeans = KMeans(n_clusters=4, random_state=42)
    clusters = kmeans.fit_predict(scaled_features)
    
    # 3. 使用随机森林预测疾病风险
    X = data[['年龄', '性别', '检查值']]  # 特征
    y = data['疾病风险等级']  # 目标变量
    
    rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
    rf_model.fit(X, y)
    feature_importance = rf_model.feature_importances_
    
    # 4. 使用PCA降维分析
    pca = PCA(n_components=2)
    pca_result = pca.fit_transform(scaled_features)
    
    # 将结果保存为JSON
    analysis_results = {
        'age_disease_correlation': age_disease_correlation.to_dict('records'),
        'cluster_centers': kmeans.cluster_centers_.tolist(),
        'cluster_labels': clusters.tolist(),
        'feature_importance': feature_importance.tolist(),
        'pca_results': pca_result.tolist()
    }
    
    with open('src/assets/analysis_results.json', 'w') as f:
        json.dump(analysis_results, f)

if __name__ == "__main__":
    analyze_data() 