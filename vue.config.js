// 可配置修改接口，即地址
module.exports = {
  devServer: {
    // 改变了vue界面的local地址
    port: 8081,
    host: 'localhost',
    // 使其运行项目时自动打开vue界面
    open: false
  },
  lintOnSave: false,
  // 添加webpack配置来解决index.html冲突问题
  configureWebpack: {
    output: {
      filename: '[name].[hash].js',
      chunkFilename: '[name].[hash].js'
    }
  },
  // 确保只有一个HTML文件输出
  pages: {
    index: {
      entry: 'src/main.js',
      template: 'public/index.html',
      filename: 'index.html',
      title: '医院信息数据管理平台'
    }
  }
}
