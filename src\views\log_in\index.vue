<template>
  <div class="login-container">
    <div class="login-background">
      <div class="circles">
        <div></div>
        <div></div>
        <div></div>
      </div>
    </div>
    <div class="login-box">
      <div class="login-header">
        <div class="logo">
          <i class="el-icon-first-aid-kit"></i>
        </div>
        <h2 class="title">体检报告分析系统</h2>
        <p class="subtitle">Medical Report Analysis System</p>
      </div>
      <el-form :model="loginForm" :rules="loginRules" ref="loginForm">
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            prefix-icon="el-icon-user"
            class="custom-input"
            placeholder="请输入用户名"
          >
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            prefix-icon="el-icon-lock"
            type="password"
            class="custom-input"
            placeholder="请输入密码"
          >
          </el-input>
        </el-form-item>
        <el-form-item>
          <div class="button-wrapper">
            <el-button type="primary" class="login-button" @click="handleLogin">
              <span>登 录</span>
              <i class="el-icon-right"></i>
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <div class="login-footer">
        <p>© 2024 体检报告分析系统 版权所有</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loginForm: {
        username: "",
        password: "",
      },
      // 输入账号和密码的验证
      loginRules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
          { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" },
        ],
      },
    };
  },

  methods: {
    handleLogin() {
      this.$router.push("/首页");
    },
  },
};
</script>

<style scoped>
.login-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.circles div {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, #8da7f5, #6b8af2);
  animation: float 15s infinite;
  opacity: 0.1;
}

.circles div:nth-child(1) {
  width: 400px;
  height: 400px;
  top: -100px;
  left: -100px;
  animation-delay: 0s;
}

.circles div:nth-child(2) {
  width: 300px;
  height: 300px;
  top: 50%;
  right: -50px;
  animation-delay: -5s;
}

.circles div:nth-child(3) {
  width: 200px;
  height: 200px;
  bottom: -50px;
  left: 30%;
  animation-delay: -10s;
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
  100% {
    transform: translateY(0) rotate(360deg);
  }
}

.login-box {
  position: relative;
  z-index: 1;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  width: 400px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo {
  font-size: 48px;
  color: #8da7f5;
  margin-bottom: 20px;
}

.title {
  color: #ffffff;
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: 600;
  letter-spacing: 2px;
}

.subtitle {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  letter-spacing: 1px;
}

.custom-input {
  margin-bottom: 20px;
}

.custom-input /deep/ .el-input__inner {
  height: 45px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #ffffff;
  font-size: 14px;
}

.custom-input /deep/ .el-input__inner:focus {
  border-color: #8da7f5;
  box-shadow: 0 0 0 2px rgba(141, 167, 245, 0.2);
}

.custom-input /deep/ .el-input__prefix {
  color: rgba(255, 255, 255, 0.4);
}

.custom-input /deep/ .el-input__inner::placeholder {
  color: rgba(255, 255, 255, 0.3);
}

.button-wrapper {
  margin-top: 30px;
}

.login-button {
  width: 100%;
  padding: 12px 0;
  font-size: 16px;
  background: linear-gradient(45deg, #8da7f5, #6b8af2);
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(141, 167, 245, 0.3);
}

.login-button:active {
  transform: translateY(0);
}

.login-footer {
  margin-top: 30px;
  text-align: center;
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
}

/* 响应式设计 */
@media screen and (max-width: 480px) {
  .login-box {
    width: 90%;
    margin: 20px;
    padding: 30px 20px;
  }

  .title {
    font-size: 24px;
  }

  .circles div {
    display: none;
  }
}
</style>
