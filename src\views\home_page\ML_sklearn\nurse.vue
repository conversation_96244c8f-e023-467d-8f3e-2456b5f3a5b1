<template>
  <div class="ml-analysis">
    <ParticleBackground />
    <el-card class="analysis-card">
      <div slot="header" class="card-header">
        <span class="header-title">智能医疗数据分析平台</span>
        <el-tag type="success">AI辅助决策</el-tag>
      </div>

      <!-- 分析控制面板 -->
      <div class="control-panel">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-select
              v-model="selectedAnalysis"
              placeholder="请选择分析类型"
              @change="handleAnalysisChange"
              class="analysis-select"
            >
              <el-option
                v-for="item in analysisOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  {{ item.description }}
                </span>
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="12">
            <el-button
              type="primary"
              @click="runAnalysis"
              :loading="loading"
              icon="el-icon-data-analysis"
              style="float: right"
            >
              开始分析
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 分析结果展示区域 -->
      <div class="analysis-result" v-loading="loading">
        <el-row :gutter="20" v-if="selectedAnalysis">
          <el-col :span="16">
            <div class="chart-container">
              <div ref="chartContainer" style="width: 100%; height: 400px"></div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="stats-panel">
              <el-card
                shadow="hover"
                v-for="(stat, index) in currentStats"
                :key="index"
              >
                <div class="stat-item">
                  <div class="stat-icon" :class="stat.type">
                    <i :class="stat.icon"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">{{ stat.value }}</div>
                    <div class="stat-label">{{ stat.label }}</div>
                  </div>
                </div>
              </el-card>
            </div>
          </el-col>
        </el-row>

        <!-- 分析结论 -->
        <div class="analysis-conclusion" v-if="selectedAnalysis">
          <el-card class="conclusion-card">
            <div slot="header">
              <span>分析结论</span>
            </div>
            <div class="conclusion-content">
              {{ currentConclusion }}
            </div>
          </el-card>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import * as echarts from "echarts";
import ParticleBackground from "@/components/ParticleBackground.vue";

export default {
  name: "MLAnalysis",
  components: {
    ParticleBackground,
  },
  data() {
    return {
      selectedAnalysis: "",
      loading: false,
      mainChart: null,
      analysisOptions: [
        {
          label: "住院时长分析",
          value: "length_of_stay",
          description: "分析患者住院时间分布",
        },
        {
          label: "医疗费用分析",
          value: "cost_analysis",
          description: "分析医疗费用情况",
        },
        {
          label: "就诊年龄分布",
          value: "age_distribution",
          description: "分析就诊人群年龄分布",
        },
        {
          label: "疾病类型统计",
          value: "disease_stats",
          description: "统计常见疾病类型",
        },
      ],
      // 模拟数据
      mockData: {
        length_of_stay: {
          chart: {
            xAxis: ["1-3天", "4-7天", "8-14天", "15-30天", "30天以上"],
            data: [150, 300, 200, 100, 50],
          },
          stats: [
            {
              label: "平均住院天数",
              value: "7.5天",
              type: "primary",
              icon: "el-icon-time",
            },
            {
              label: "最长住院时间",
              value: "45天",
              type: "warning",
              icon: "el-icon-warning",
            },
            {
              label: "样本总数",
              value: "800例",
              type: "info",
              icon: "el-icon-document",
            },
          ],
          conclusion:
            "数据显示大多数患者的住院时间集中在4-7天，建议医院据此优化床位调配。",
        },
        cost_analysis: {
          chart: {
            xAxis: ["0-5000", "5000-10000", "10000-20000", "20000以上"],
            data: [400, 300, 200, 100],
          },
          stats: [
            {
              label: "平均费用",
              value: "8,500元",
              type: "primary",
              icon: "el-icon-money",
            },
            {
              label: "最高费用",
              value: "35,000元",
              type: "warning",
              icon: "el-icon-warning",
            },
            {
              label: "样本总数",
              value: "1000例",
              type: "info",
              icon: "el-icon-document",
            },
          ],
          conclusion:
            "医疗费用主要集中在5000-10000元区间，建议加强医保政策宣传。",
        },
        age_distribution: {
          chart: {
            xAxis: ["0-18岁", "19-30岁", "31-45岁", "46-60岁", "60岁以上"],
            data: [100, 200, 300, 250, 150],
          },
          stats: [
            {
              label: "平均年龄",
              value: "42岁",
              type: "primary",
              icon: "el-icon-user",
            },
            {
              label: "主要年龄段",
              value: "31-45岁",
              type: "warning",
              icon: "el-icon-warning",
            },
            {
              label: "样本总数",
              value: "1000人",
              type: "info",
              icon: "el-icon-document",
            },
          ],
          conclusion: "就诊人群以中年人为主，建议加强对该年龄段的健康管理。",
        },
        disease_stats: {
          chart: {
            names: ["呼吸系统", "消化系统", "心血管", "骨科", "其他"],
            data: [300, 250, 200, 150, 100],
          },
          stats: [
            {
              label: "最常见类型",
              value: "呼吸系统",
              type: "primary",
              icon: "el-icon-first-aid-kit",
            },
            {
              label: "占比",
              value: "30%",
              type: "warning",
              icon: "el-icon-warning",
            },
            {
              label: "样本总数",
              value: "1000例",
              type: "info",
              icon: "el-icon-document",
            },
          ],
          conclusion: "呼吸系统疾病占比最高，建议加强相关科室建设。",
        },
      },
    };
  },
  computed: {
    currentStats() {
      return this.selectedAnalysis
        ? this.mockData[this.selectedAnalysis].stats
        : [];
    },
    currentConclusion() {
      return this.selectedAnalysis
        ? this.mockData[this.selectedAnalysis].conclusion
        : "";
    },
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (this.mainChart) {
      this.mainChart.dispose();
    }
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    initChart() {
      if (!this.mainChart && this.$refs.chartContainer) {
        this.mainChart = echarts.init(this.$refs.chartContainer);
        window.addEventListener("resize", this.handleResize);
      }
    },
    handleResize() {
      if (this.mainChart) {
        this.mainChart.resize();
      }
    },
    handleAnalysisChange() {
      if (this.selectedAnalysis) {
        this.$nextTick(() => {
          this.runAnalysis();
        });
      }
    },
    runAnalysis() {
      this.loading = true;
      
      // 确保 DOM 已更新
      this.$nextTick(() => {
        setTimeout(() => {
          this.initChart(); // 确保图表已初始化
          this.renderChart();
          this.loading = false;
        }, 100);
      });
    },
    renderChart() {
      if (!this.mainChart) {
        this.initChart();
      }
      
      if (!this.mainChart) {
        console.error('Chart initialization failed');
        return;
      }
      const data = this.mockData[this.selectedAnalysis];
      let option = {};

      if (this.selectedAnalysis === "disease_stats") {
        // 饼图配置
        option = {
          title: { text: "疾病类型分布" },
          tooltip: { trigger: "item" },
          legend: { orient: "vertical", left: "left" },
          series: [
            {
              type: "pie",
              radius: "50%",
              data: data.chart.names.map((name, index) => ({
                name: name,
                value: data.chart.data[index],
              })),
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
              },
            },
          ],
        };
      } else {
        // 柱状图配置
        option = {
          title: {
            text: this.analysisOptions.find(
              (opt) => opt.value === this.selectedAnalysis
            ).label,
          },
          tooltip: { trigger: "axis" },
          xAxis: {
            type: "category",
            data: data.chart.xAxis,
          },
          yAxis: {
            type: "value",
          },
          series: [
            {
              data: data.chart.data,
              type: "bar",
              showBackground: true,
              backgroundStyle: {
                color: "rgba(180, 180, 180, 0.2)",
              },
            },
          ],
        };
      }

      this.mainChart.setOption(option);
    },
  },
   // 在组件更新后重新初始化图表
   updated() {
    this.$nextTick(() => {
      this.initChart();
    });
  },

    // 在组件激活时重新初始化图表
    activated() {
    this.$nextTick(() => {
      this.initChart();
    });
  }
};
</script>

<style scoped>
.ml-analysis {
  padding: 20px;
  min-height: 100vh;
  position: relative;
}

.analysis-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-title {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.control-panel {
  background: rgba(248, 249, 250, 0.9);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.analysis-select {
  width: 100%;
}

.chart-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.stats-panel {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.stat-icon.primary {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

.stat-icon.warning {
  background: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
}

.stat-icon.info {
  background: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.conclusion-card {
  margin-top: 20px;
}

.conclusion-content {
  padding: 10px;
  color: #606266;
  line-height: 1.6;
}

@media screen and (max-width: 768px) {
  .el-col {
    width: 100% !important;
  }

  .control-panel .el-button {
    float: none !important;
    margin-top: 10px;
    width: 100%;
  }
}
</style>
