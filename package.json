{"name": "hospital-vue", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@jiaminghi/bezier-curve": "^0.0.9", "@jiaminghi/c-render": "^0.4.3", "@jiaminghi/charts": "^0.2.18", "@jiaminghi/color": "^1.1.3", "@jiaminghi/data-view": "^2.10.0", "@jiaminghi/transition": "^1.1.11", "axios": "^1.7.7", "base-64": "^1.0.0", "camelcase-css": "^2.0.1", "core-js": "^3.8.3", "cors": "^2.8.5", "crypto-js": "^4.2.0", "echarts": "^5.5.1", "echarts-liquidfill": "^3.1.0", "echarts-wordcloud": "^2.1.0", "element-ui": "^2.15.14", "particles.js": "^2.0.0", "ts-interface-checker": "^1.0.2", "vue": "^2.6.14", "vue-json-excel": "^0.3.0", "vue-particles": "^1.0.9", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "autoprefixer": "^10.4.20", "less": "^4.2.0", "less-loader": "^12.2.0", "postcss": "^8.4.45", "tailwindcss": "^3.4.10", "vue-particles": "^1.0.9", "vue-template-compiler": "^2.6.14"}}