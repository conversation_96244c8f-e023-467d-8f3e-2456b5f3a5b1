<template>
    <div>
     <button class="button">Click Me</button>
    </div>
   </template>
   
   <script>
   export default {
     name: 'SlideDownButton'
   }
   </script>
   
   <style>
  .button {
   text-align: center;
  
   cursor: pointer;
   font-size: 15px; /* 缩小字体大小 */
   letter-spacing: 1px; /* 缩小字间距 */
   position: relative;
   background: linear-gradient(to right, #dbcae2, #ab9ec4); /* 淡紫色渐变背景 */
   border: none;
   color: #fff;
   padding: 5px; /* 缩小内边距 */
   width: 100px; /* 缩小按钮宽度 */
   transition-duration: 0.4s;
   overflow: hidden;
   box-shadow: 0 5px 15px #193047;
   border-radius: 4px;
 }
 
 .button:hover {
   background: #fff;
   box-shadow: 0px 2px 10px 5px #a4cbc3;
   color: #000;
 }
 
 .button:after {
   content: "";
   background: #ccbfe7; /* 渐变的结束颜色 */
   display: block;
   position: absolute;
   padding-top: 300%;
   padding-left: 350%;
   margin-left: -20px !important;
   margin-top: -120%;
   opacity: 0;
   transition: all 0.8s;
 }
 
 .button:active:after {
   padding: 0;
   margin: 0;
   opacity: 1;
   transition: 0s;
 }
 
 .button:focus { outline:0; }
   </style>
   