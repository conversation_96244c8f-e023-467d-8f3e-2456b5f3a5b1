* {
   
    box-sizing: border-box;
}

li {
    list-style: none;
}

@font-face {
    font-family: electronicFont;
    src: url(font/DS-DIGIT.TTF);
}

body {
    font-family: Arial, Helvetica, sans-serif;
    margin: 0;
    padding: 0;
    /*  背景图定位 / 背景图尺寸  cover 完全铺满容器  contain 完整显示在容器内 */
    /* background: url(images/bg2.jpg) no-repeat #000; */
    background: linear-gradient(150deg, #c0c6d8, #d3dcf8);
    background-size: cover;
    /* 行高是字体1.15倍 */
    line-height: 1.15;
    height: 100%;
}
.index-header {
    display: flex;
    justify-content: center;
    align-items: center;
  }

/* header {
    position: relative;
    height: 1.25rem;
   
    background-size: 100% 100%;
}

header h1 {
    font-size: 0.475rem;
    color: #fff;
    text-align: center;
    line-height: 1rem;
} */


header .showTime {
    position: absolute;
    top: 0;
    right: 0.375rem;
    line-height: 0.9375rem;
    font-size: 0.25rem;
    background: linear-gradient(150deg, #f8f9fa, #e9ecef);
}

.mainbox {
   width:100%;
    padding: 0.125rem 0.125rem 0;
    display: flex;
    color:#91abf8;
}

.mainbox .column {
    flex: 4;
}
.mycolume{
    flex:6;
}
.hsicolum{
    flex:4;
}
.mainbox .column:nth-child(2) {
    flex: 6;
    margin: 0 0.125rem 0.1875rem;
    overflow: hidden;
}



.panel::before {
    position: absolute;
    top: 0;
    left: 0;
    content: "";
    width: 10px;
    height: 10px;
    border-top: 2px solid #02a6b5;
    border-left: 2px solid #02a6b5;
}

.panel::after {
    position: absolute;
    top: 0;
    right: 0;
    content: "";
    width: 10px;
    height: 10px;
    border-top: 2px solid #02a6b5;
    border-right: 2px solid #02a6b5;
}

.panel .panel-footer {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
}

.panel .panel-footer::before {
    position: absolute;
    bottom: 0;
    left: 0;
    content: "";
    width: 10px;
    height: 10px;
    border-bottom: 2px solid #02a6b5;
    border-left: 2px solid #02a6b5;
}

.panel .panel-footer::after {
    position: absolute;
    bottom: 0;
    right: 0;
    content: "";
    width: 10px;
    height: 10px;
    border-bottom: 2px solid #02a6b5;
    border-right: 2px solid #02a6b5;
}

.panel h2 {
    height: 0.5rem;
    line-height: 0.6rem;
    text-align: center;
    color: #fff;
    font-size: 0.25rem;
    font-weight: 400;
}

.panel h2 a {
    margin: 0 0.1875rem;
    color: #fff;
    text-decoration: underline;
}



.no {
    background: rgba(101, 132, 226, 0.1);
    
    padding: 0.1875rem;
    height: 180px;
}

.no .no-hd {
    position: relative;
    border: 1px solid rgba(25, 186, 139, 0.17);
}

.no .no-hd::before {
    content: "";
    position: absolute;
    width: 30px;
    height: 10px;
    border-top: 2px solid #02a6b5;
    border-left: 2px solid #02a6b5;
    top: 0;
    left: 0;
}

.no .no-hd::after {
    content: "";
    position: absolute;
    width: 30px;
    height: 10px;
    border-bottom: 2px solid #02a6b5;
    border-right: 2px solid #02a6b5;
    right: 0;
    bottom: 0;
}

.no .no-hd ul {
    display: flex;
}

.no .no-hd ul li {
    position: relative;
    flex: 1;
    text-align: center;
    height: 1rem;
    line-height: 1rem;
    font-size: 0.875rem;
    color: #ffeb7b;
    padding: 0.05rem 0;
    font-family: electronicFont;
    font-weight: bold;
}

.no .no-hd ul li:first-child::after {
    content: "";
    position: absolute;
    height: 50%;
    width: 1px;
    background: rgba(255, 255, 255, 0.2);
    right: 0;
    top: 25%;
}

.no .no-bd ul {
    display: flex;
}

.no .no-bd ul li {
    flex: 1;
    height: 0.5rem;
    line-height: 0.5rem;
    text-align: center;
    font-size: 0.225rem;
    color: rgba(255, 255, 255, 0.7);
    padding-top: 0.125rem;
}

.selecter {
    margin-top: 10px;
    height: 90px;
}

.selecter p {
    text-align: center;
    color: #fff;
    display: block;
    margin-bottom: 15px;
    margin-top: 5px;
}



.select_form .options {
    font-size: 15px;
    color: rgba(255, 255, 255, 0.7);
}

.select_form .options_frame {
    display: block;
    width: 85px;
    height: 35px;
}

.tabler {
    height: 310px;
}

.tabler p {
    display: block;
    text-align: center;
    margin-top: 10px;
}

@keyframes rotate {
    from {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes rotate1 {
    from {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    to {
        transform: translate(-50%, -50%) rotate(-360deg);
    }
}

@media screen and (max-width: 1024px) {
    html {
        font-size: 42px !important;
    }
}

@media screen and (min-width: 1920) {
    html {
        font-size: 80px !important;
    }
}

.el-table .warning-row {
    background: oldlace;
}

.el-table .success-row {
    background: #f0f9eb;
}
.panel.selecter {
    font-family: Arial, sans-serif; /* 设置字体 */
    height:150px;
    border-radius: 8px; /* 边框圆角 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 盒子阴影 */
  }
  
  .panel.selecter p {
    font-size: 20px;/* 标题字体大小 */
    color: '#333'; /* 标题颜色 */
  }
  
  .select_form {
    margin-bottom: 15px; /* 每个选择器之间的间距 */
  }
  
  .options {
    font-size: 1em; /* 标签字体大小 */
    color: #666; /* 标签颜色 */
    display: block; /* 标签显示为块级元素 */
    margin-bottom: 5px; /* 标签与选择框的间距 */
  }
  
  .options_frame {
    width: 25%; /* 选择框宽度 */
    height:20px;
    padding: 8px 12px; /* 选择框内边距 */
    border: 1px solid #ccc; /* 选择框边框 */
    border-radius: 4px; /* 选择框圆角 */
    background-color: ; /* 选择框背景颜色 */
    font-size: 1em; /* 选择框字体大小 */
    color: #333; /* 选择框文字颜色 */
    appearance: none; /* 移除默认的样式 */
    -webkit-appearance: none; /* Safari 浏览器兼容 */
    -moz-appearance: none; /* Firefox 浏览器兼容 */
    background-image: url('data:image/svg+xml;utf8,<svg fill="%23333333" height="30" viewBox="0 0 24 24" width="30" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/><path d="M0 0h24v24H0z" fill="none"/></svg>'); /* 下拉箭头 */
    background-repeat: no-repeat;
    background-position: right 10px center; /* 下拉箭头位置 */
    cursor: pointer; /* 鼠标指针样式 */
  }
  
 
.highlight {
    background-color:#bde775; /* 背景色 */
    color: #333; /* 文本颜色 */
    padding: 2px 6px; /* 内边距 */
    border-radius: 4px; /* 边框圆角 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 阴影 */
    font-weight: bold; /* 字体加粗 */
    display: inline-block; /* 行内块级元素 */
  }
  scroll-board {
    width: 600px;
    height: 400px;
    overflow: hidden;
    border: 2px solid #4CAF50;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    background-color: #fff;
  }
  
  .scroll-board {
    width: 100%;
    height: 350px;
    overflow: hidden;
    border: 2px solid;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    
  }
  
  .header {
    display: flex;
    background:#b1c4fc;
    color:#fff;
    width:100%;
    font-weight: bold;
    text-align: center;
  }
  
  .header-cell {
    flex: 10;
    padding: 10px;
    border-right: 1px solid #fff;
  }
  
  .header-cell:last-child {
    border-right: none; /* 去掉最后一个单元格的右边框 */
  }
  
  .body {
    transition: transform 0.5s ease;
    height: calc(400px - 50px); /* 总高度减去表头高度 */
  }
  
  .row {
    display: flex;
    height: 70px; /* 行高 */
    align-items: center;
    border-bottom: 1px solid #f1f1f1;
  }
  
  .cell {
    flex: 7;
    padding: 15px;
    text-align: center;
    transition: background-color 0.3s;
    color:#000;
    
     /* 将字体颜色改为黑色 */
    background-color: transparent; /* 设置单元格背景为透明 */
  }
  
  .row:nth-child(odd) .cell {
    /* 奇数行渐变背景颜色 */
    background: linear-gradient(150deg, #e0eeff, #c0d4f2);
}

.row:nth-child(even) .cell {
    /* 偶数行渐变背景颜色 */
    background: linear-gradient(150deg, #d0deff, #b0c4e2);
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'Electronic';
  src: url('./font/DS-DIGIT.TTF') format('truetype');
}

.text-gradient {
  /* 背景渐变 */
  background-image: linear-gradient(to bottom, #e5e4ea, #5EA8F2);
  /* 元素背景延伸到文本 */
  -webkit-background-clip: text;
  /* 文本字符填充颜色透明 */
  -webkit-text-fill-color: transparent;

}

.dv-scroll-board .dv-row:nth-child(odd) {
    background-color: #f0f0f0; /* 浅灰色 */
  }
  
  .dv-scroll-board .dv-row:nth-child(even) {
    background-color: #e0e0e0; /* 深灰色 */
  }

  button {
    font-size: 20px; /* 缩小字体大小 */
    font-family: Raleway; /* 字体 */
    line-height: 25px; /* 行高，适应新的字体大小 */
    padding: 0.4em 0.8em; /* 缩小内边距，以减少按钮的整体高度和宽度 */
    border-radius: 8px 8px 10px 10px; /* 调整圆角 */
    box-shadow:
        0px 6px 0px 0px #A3C1DA, /* 按钮厚度，使用淡蓝色 */
        0px 0 15px rgba(255, 255, 255, 0.2) inset, /* 内部发光效果 */
        2px 20px 0px rgba(255, 255, 255, 0.1) inset, /* 细微反射效果 */
        4px 12px 20px -10px rgba(0, 0, 0, 0.3); /* 深色阴影 */
    border: 1px solid #A3C1DA; /* 边框颜色为淡蓝色 */
    cursor: pointer; /* 鼠标悬停时显示为指针 */
    background: #B2DFF7; /* 按钮背景色为淡蓝色 */
    color: #2C3E50; /* 文本颜色为深色，以便与背景形成对比 */
    text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.5); /* 文本阴影效果，增加可读性 */
    transform: rotateX(5deg); /* 稍微倾斜按钮视角 */
    margin: 10px; /* 按钮外边距 */
    width: 200px; /* 缩小按钮的宽度 */
    text-align: center; /* 文本中心对齐 */
}

button i {
     /* 图标与文本的间距 */
    transform: scale(1.1) translate(0, -1px); /* 图标轻微放大和垂直移动 */
}

button:hover {
    margin-top: 12px; /* 悬停时调整外边距，制造浮动效果 */
    margin-bottom: 6px; /* 悬停时底部外边距 */
    box-shadow:
        0px 4px 0px 0px #A3C1DA, /* 按钮厚度 */
        0px 0 30px rgba(173, 216, 230, 1) inset, /* 内发光效果，使用淡蓝色 */
        4px 25px 0px rgba(255, 255, 255, 0.15) inset, /* 反射效果 */
        0px 0px 20px rgba(173, 216, 230, 0.3), /* 外发光效果 */
        4px 12px 20px -10px rgba(0, 0, 0, 0.3); /* 深色阴影 */
    color: #fff; /* 悬停时文本颜色变为白色 */
    filter: saturate(1.2); /* 悬停时略微增强饱和度 */
}

button:hover i {
    transform: rotate(-10deg) translate(1px, -3px) scale(1.1); /* 悬停时图标轻微旋转和放大 */
}

button:active {
    margin-top: 14px; /* 点击时调整外边距 */
    margin-bottom: 4px; /* 点击时底部外边距 */
    box-shadow:
        0px 2px 0px 0px #A3C1DA, /* 按钮厚度 */
        0px 0 50px 5px rgba(173, 216, 230, 1) inset, /* 内发光效果 */
        4px 20px 0px rgba(255, 255, 255, 0.1) inset, /* 反射效果 */
        0px 0px 20px rgba(173, 216, 230, 0.5), /* 外发光效果 */
        4px 12px 20px -10px rgba(0, 0, 0,0.3); /* 深色阴影 */
    filter: saturate(1.5); /* 点击时增加饱和度 */
}

button:active i {
    transform: rotate(-20deg) translate(2px, -5px) scale(1.2); /* 点击时图标旋转和放大，增加动态效果 */
}

/* 定义蓝色按钮样式，保持原样 */
/* 定义淡蓝色按钮样式 */
.blue {
    background-color: #8DA7F5; /* 设置背景色为 #8DA7F5 */
    border: 1px solid #8DA7F5; /* 边框颜色也设置为 #8DA7F5 */
    color: #2C3E50; /* 文本颜色为深色，以便与背景形成对比 */
    filter: none; /* 移除之前的色相和饱和度调整 */
}

.blue:hover {
    background-color: #A1B8E5; /* 悬停时背景色稍微变浅 */
    border-color: #A1B8E5; /* 悬停时边框颜色变浅 */
    color: #fff; /* 悬停时文本颜色变为白色 */
}

.blue:active {
    background-color: #7A94D8; /* 点击时背景色变得更深 */
    border-color: #7A94D8; /* 点击时边框颜色变深 */
    color: #ffffff; /* 点击时文本颜色为白色 */
}
