import axios from 'axios';

// 配置基础URL
const API_BASE_URL = 'http://127.0.0.1:8000'; // 修改为本地FastAPI服务器地址

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  // 添加跨域支持
  withCredentials: false
});

export const performAnalysis = async (analysisType, params = {}) => {
  try {
    console.log('Sending request with params:', { analysisType, params }); // 调试日志
    const response = await apiClient.post('/api/ml-analysis', {
      analysisType,
      params: {
        ...params,
        dateRange: params.dateRange ? [
          params.dateRange[0].toISOString().split('T')[0],
          params.dateRange[1].toISOString().split('T')[0]
        ] : null
      }
    });
    console.log('Response received:', response.data); // 调试日志
    return response.data.data;
  } catch (error) {
    console.error('Analysis request failed:', error); // 调试日志
    if (error.response) {
      // 服务器响应的错误
      throw new Error(`分析请求失败: ${error.response.data.detail || error.response.data || '未知错误'}`);
    } else if (error.request) {
      // 请求发送失败
      throw new Error('无法连接到服务器，请确保服务器已启动');
    } else {
      // 其他错误
      throw new Error(`请求错误: ${error.message}`);
    }
  }
};

// 获取模型训练状态
export const getModelStatus = async () => {
  try {
    const response = await axios.get('/api/ml-analysis/status');
    return response.data;
  } catch (error) {
    throw new Error('获取模型状态失败：' + error.message);
  }
};

// 获取特定患者的预测结果
export const getPatientPrediction = async (patientId) => {
  try {
    const response = await axios.get(`/api/ml-analysis/patient/${patientId}`);
    return response.data;
  } catch (error) {
    throw new Error('获取患者预测失败：' + error.message);
  }
};

// 这里添加您的 API 相关代码
export default {
    // 在此处添加您的 API 方法
} 