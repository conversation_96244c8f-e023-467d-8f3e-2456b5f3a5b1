<template>
  <div>
    <div>
      <section class="mainbox">
        <div class="column" style="flex-direction: column">
          <div class="panel liquid" style="height: 190px">
            <h2
              style="
                font-size: 20px;
                font-weight: bold;
                color: #8da7f5;
                letter-spacing: 2px;
              "
            >
              患者性别占比
            </h2>
            <div ref="liquidchart" style="width: 100%; height: 190px"></div>
            <div class="panel-footer"></div>
          </div>
          <div class="panel rose" style="height: 330px">
            <h2
              style="
                font-size: 20px;
                font-weight: bold;
                color: #8da7f5;
                letter-spacing: 2px;
              "
            >
              器官患病情况图
            </h2>
            <div class="chart" style="width: 100%; height: 280px"></div>
            <div class="panel-footer"></div>
          </div>
        </div>
        <div class="column">
          <div class="panel no">
            <div>
              <div
                class="text-[#ecfeff] text-center font-serif"
                style="font-size: 22px"
              >
                病区总人数:
                <span
                  class="ml-1 mr-1 font-bold font-[Electronic] bg-clip-text text-transparent bg-gradient-to-r from-yellow-200 to-yellow-400"
                  style="font-size: 45px"
                  >23930</span
                >
                人
              </div>
              <div class="flex flex-wrap" style="font-size: 20px">
                <div class="w-1/3 text-center text-[#ecfeff] font-serif">
                  十病区:
                  <span
                    ref="city1"
                    class="bg-clip-text text-transparent bg-gradient-to-r from-yellow-200 to-yellow-400 font-[Electronic]"
                    style="font-size: 35px"
                  >
                    738
                  </span>
                  人
                </div>
                <div class="w-1/3 text-center text-[#ecfeff] font-serif">
                  五病区:
                  <span
                    ref="city2"
                    class="bg-clip-text text-transparent bg-gradient-to-r from-yellow-200 to-yellow-400 font-[Electronic]"
                    style="font-size: 35px"
                  >
                    669
                  </span>
                  人
                </div>
                <div class="w-1/3 text-center text-[#ecfeff] font-serif">
                  六病区:
                  <span
                    ref="city3"
                    class="bg-clip-text text-transparent bg-gradient-to-r from-yellow-200 to-yellow-400 font-[Electronic]"
                    style="font-size: 35px"
                  >
                    645
                  </span>
                  人
                </div>
                <div class="w-1/3 text-center text-[#ecfeff] font-serif">
                  七病区:
                  <span
                    ref="city4"
                    class="bg-clip-text text-transparent bg-gradient-to-r from-yellow-200 to-yellow-400 font-[Electronic]"
                    style="font-size: 35px"
                  >
                    605
                  </span>
                  人
                </div>
                <div class="w-1/3 text-center text-[#ecfeff] font-serif">
                  九病区:
                  <span
                    ref="city5"
                    class="bg-clip-text text-transparent bg-gradient-to-r from-yellow-200 to-yellow-400 font-[Electronic]"
                    style="font-size: 35px"
                  >
                    514
                  </span>
                  人
                </div>
                <div class="w-1/3 text-center text-[#ecfeff] font-serif">
                  七病区:
                  <span
                    ref="city6"
                    class="bg-clip-text text-transparent bg-gradient-to-r from-yellow-200 to-yellow-400 font-[Electronic]"
                    style="font-size: 35px"
                  >
                    480
                  </span>
                  人
                </div>
              </div>
            </div>
          </div>
          <!-- 年龄结构图 -->
          <div class="panel barchart" style="height: 340px">
            <div ref="barchart" style="width: 100%; height: 340px"></div>
            <div class="panel-footer"></div>
          </div>
        </div>
      </section>
    </div>
    <!-- 不同年龄段患病人数占比与病症信息 -->
    <div class="panel age_disease" style="height: 320px">
      <h2
        style="
          font-size: 20px;
          font-weight: bold;
          color: #8da7f5;
          letter-spacing: 2px;
        "
      >
        不同年龄段患病人数占比与病症信息
      </h2>
      <div style="display: flex">
        <div id="radarchart" style="width: 400px; height: 300px"></div>
        <div id="piechart" style="width: 400px; height: 300px"></div>
        <div v-if="selectedDiagnoses.length">
          <h3 style="color: #fff">
            Diagnoses for {{ selectedAgeGroup }} years
          </h3>
          <div
            v-html="renderWordCloud(selectedDiagnoses)"
            style="color: #fff"
          ></div>
        </div>
      </div>
      <div class="panel-footer"></div>
    </div>
    <!-- 每日出院于入院人数图 -->
    <div class="panel line" style="height: 290px">
      <h2
        style="
          font-size: 20px;
          font-weight: bold;
          color: #8da7f5;
          letter-spacing: 5px;
          margin-bottom: 10px;
        "
      >
        每日出院于入院人数
      </h2>
      <div ref="linebar" style="width: 100%; height: 270px"></div>
      <div class="panel-footer"></div>
    </div>
  </div>
</template>

<script>
import "./js/jquery.js";
import "./js/flexible.js";
import * as echarts from "echarts";
// import "echarts-wordcloud";
import "echarts-liquidfill";
import data from "./data/age_disease.json";
//患者性别比例图
// 注册液态填充系列
echarts.registerTheme("liquidFill", {
  // 可以添加自定义主题配置
});

export default {
  // 组件挂载完成后初始化图表
  mounted() {
    //各年龄阶段信息展示图
    this.initRadarChart();
    this.initPieChart();
    //年龄结构图
    this.initBarChart();
    //玫瑰图
    this.initChartRose();
    //云词图
    // this.initWorld();
    // this.startAutoUpdateWordCloud();
    // window.addEventListener("resize", this.onResize);
    //云词图

    // this.startScroll();

    //患者性别比例水球图
    this.LiquidFillChart();

    //折线图+柱状图
    this.initLinebar();
    this.fetchDynamicData();
    this.startDynamicUpdate(); // 开始动态更新数据
  },
  beforeDestroy() {
    clearInterval(this.scrollInterval);
    window.removeEventListener("resize", this.onResize);
    window.removeEventListener("resize", this.resize);
    if (this.chartDom) {
      this.chartDom.dispose();
    }

    // 清除定时器(信息表)
    // if (this.intervalId) {
    //   clearInterval(this.intervalId);
    // }
    // 清除滚动定时器(信息表)
    // clearInterval(this.scrollInterval);
    //清除年龄结构图
    if (this.Barchart) {
      this.Barchart.dispose();
    }
  },

  methods: {
    //给各年龄阶段信息展示图
    initRadarChart() {
      const radarChart = echarts.init(document.getElementById("radarchart"));
      const ageGroups = Object.keys(this.data);
      const totalCounts = ageGroups.map(
        (group) => this.data[group].total_count
      );

      const option = {
        title: {
          text: "Age Group Total Count",
          textStyle: {
            color: "#fff", // 标题字体颜色
            fontSize: 15, // 字体大小
          },
        },
        tooltip: {},
        radar: {
          indicator: ageGroups.map((group) => ({
            name: group,
            max: Math.max(...totalCounts),
          })),
          name: {
            textStyle: {
              color: "#fff", // 名称字体颜色
            },
          },
        },
        series: [
          {
            name: "Total Count",
            type: "radar",
            data: [
              {
                value: totalCounts,
                name: "Total Count",
              },
            ],
          },
        ],
      };

      radarChart.setOption(option);
    },
    initPieChart() {
      const pieChart = echarts.init(document.getElementById("piechart"));
      const ageGroups = Object.keys(this.data);
      const totalCounts = ageGroups.map(
        (group) => this.data[group].total_count
      );
      const total = totalCounts.reduce((sum, count) => sum + count, 0);

      const option = {
        title: {
          text: "Age Group Distribution",
          subtext: "Click to see details",
          left: "center",
          textStyle: {
            color: "#fff", // 标题字体颜色
            fontSize: 15, // 字体大小
          },
        },
        tooltip: {
          trigger: "item",
          formatter: (params) => {
            const percent = ((params.value / total) * 100).toFixed(2);
            return `${params.name}:  ${percent}%`;
          },
        },
        series: [
          {
            name: "Age Groups",
            type: "pie",
            radius: "50%",
            data: ageGroups.map((group, index) => ({
              value: totalCounts[index],
              name: group,
              itemStyle: {
                // 为不同的年龄组设置不同的颜色
                color: this.getColor(index),
              },
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };

      pieChart.setOption(option);

      pieChart.on("click", (params) => {
        const ageGroup = params.name;
        this.selectedAgeGroup = ageGroup;
        this.selectedDiagnoses = this.data[ageGroup].diagnoses.map((d) => d[0]);
      });
    },
    getColor(index) {
      const colors = [
        "#00acee",
        "#52cdd5",
        "#79d9f1",
        "#a7e7ff",
        "#c8efff",
        "#9966FF",
        "#FF6384",
      ];
      return colors[index % colors.length];
    },
    renderWordCloud(diagnoses) {
      const frameStyle = `style="border: 1px solid #D8BFD8; background-color: rgba(101, 132, 226, 0.1); padding: 10px; border-radius: 5px; width: 300px; height: 200px; overflow: auto; display: flex; flex-wrap: wrap; justify-content: center; align-items: center;"`;
      const diagnosisStyle = `style="margin: 5px; display: inline-block; font-size: 15px; color: #ffffff;"`;
      const html = diagnoses
        .map((diagnosis) => `<span ${diagnosisStyle}>${diagnosis}</span>`)
        .join("");

      return `<div ${frameStyle}>${html}</div>`;
    },
    //年龄结构图
    initBarChart() {
      const option = {
        baseOption: {
          title: {
            text: "年龄结构图",
            left: "center",
            textStyle: {
              color: "#8DA7F5", // 标题颜色
              fontSize: 20, // 标题字体大小
              fontWeight: "bold",
            },
          },
          timeline: {
            axisType: "category",
            autoPlay: true,
            currentIndex: 0,
            playInterval: 2000,
            label: {
              normal: {
                color: "#e4bfad",
                fontSize: 18,
              },
            },
            data: this.dataAge.map((item) => item.year),
          },

          legend: {
            data: ["男性", "女性"],
            top: "0%",
            right: "5%",
            textStyle: {
              color: "#fff",
            },
          },
          tooltip: {
            formatter: "{b}<br/>{a}: {c}%",
            show: true,
          },
          grid: [
            {
              top: "12%",
            },
          ],
          xAxis: [
            {
              type: "value",
              inverse: true,
              max: 30,
              axisLine: { show: false },
              axisTick: { show: false },
              splitLine: {
                show: true,
                lineStyle: { color: "#fff", type: "dashed" },
              },
              axisLabel: {
                textStyle: { color: "#fff" },
              },
            },
          ],
          yAxis: [
            {
              type: "category",
              data: this.ageGap,
              axisLine: {
                lineStyle: { color: "#e4bfad" },
              },
              axisLabel: {
                textStyle: { color: "#ffff" },
              },
            },
          ],
          series: [
            {
              name: "男性",
              type: "bar",
              data: [],
              itemStyle: {
                borderRadius: 10,
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: "#e0c3fc" },
                    { offset: 1, color: "#8ec5fc" },
                  ]),
                  borderRadius: 10,
                },
              },
              barWidth: "40%", // Increased bar width
              label: {
                show: true,
                position: "inside",
                formatter: "{c}%",
                textStyle: { color: "#fff" },
              },
            },
            {
              name: "女性",
              type: "bar",
              data: [],
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: "#a8e0f1" },
                    { offset: 1, color: "#b2f1d5" },
                  ]),
                  borderRadius: 10,
                },
              },
              barWidth: "40%",
              label: {
                show: true,
                position: "inside",
                formatter: "{c}%",
                textStyle: { color: "#fff" },
              },
            },
          ],
        },
        options: this.dataAge.map((item) => ({
          series: [
            {
              data: item.age_data.male,
            },
            {
              data: item.age_data.female,
            },
          ],
        })),
      };

      this.Barchart = echarts.init(this.$refs.barchart);
      this.Barchart.setOption(option);
    },
    //表单滚动
    // loadData() {
    //   try {
    //     // 使用 require 导入 JSON 数据
    //     const jsonData = require("./data/patients"); // 确保路径正确
    //     // 将 JSON 数据转换为表格显示所需的格式
    //     this.data = jsonData.map((item) => [
    //       item.code,
    //       item.main_symptoms,
    //       item.symptoms1,
    //       item.symptoms2,
    //     ]);
    //   } catch (error) {
    //     console.error("获取数据时出错:", error);
    //   }
    // },
    // startScroll() {
    //   this.scrollInterval = setInterval(() => {
    //     this.currentIndex = (this.currentIndex + 1) % this.data.length;
    //   }, 1000); // 每1秒滚动一次
    // },

    //玫瑰图
    initChartRose() {
      this.chartDom = echarts.init(document.querySelector(".rose  .chart"));
      this.updateChart(this.roseChartData);
    },
    updateChart(datak) {
      var option = {
        legend: {
          top: "bottom",
          orient: "vertical",
          left: "left",
          textStyle: {
            color: "#fff",
          },
        },
        tooltip: {
          trigger: "item",
          textStyle: {
            color: "#000",
          },
          formatter: "{b}:{c}({d})%",
        },
        series: [
          {
            type: "pie",
            radius: [40, 120],
            center: ["50%", "50%"],
            roseType: "area",
            itemStyle: {
              borderRadius: 10,
            },
            data: datak,
            emphasis: {
              itemStyle: {
                shadowBlur: 18,
                shadowOffsetX: 5,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      this.chartDom.setOption(option);

      // 玫瑰图数据动态展示
      let currentIndex = -1;
      setInterval(() => {
        var dataLen = option.series[0].data.length;
        // 取消之前高亮的图形
        this.chartDom.dispatchAction({
          type: "downplay",
          seriesIndex: 0,
          dataIndex: currentIndex,
        });
        currentIndex = (currentIndex + 1) % dataLen;
        // 高亮当前图形
        this.chartDom.dispatchAction({
          type: "highlight",
          seriesIndex: 0,
          dataIndex: currentIndex,
        });
        // 显示 tooltip
        this.chartDom.dispatchAction({
          type: "showTip",
          seriesIndex: 0,
          dataIndex: currentIndex,
        });
      }, 500);

      // 添加点击事件，点击柱状图部分查看更多详细信息
      this.chartDom.on("click", (params) => {
        alert(params.name + "患病人数为" + params.value + "人");
        // 这里可以根据点击的具体数据执行相应的操作，比如展示更多详细信息等
      });

      // 当我们浏览器缩放的时候，图表也等比例缩放
      window.addEventListener("resize", () => {
        this.chartDom.resize();
      });
    },
    //患者性别占比水球图
    LiquidFillChart() {
      const chart = echarts.init(this.$refs.liquidchart);
      const option = {
        tooltip: {
          show: true,
          formatter: (params) => {
            if (params.seriesType === "liquidFill") {
              return `${params.seriesName}: ${params.value * 100}% (${
                params.seriesName === "男性" ? "15054人" : "8876人"
              })`;
            }
          },
        },
        series: [
          {
            name: "男性",
            type: "liquidFill",
            shape: "circle",
            radius: "65%",
            center: ["25%", "50%"],
            data: [0.63], // 男性占总人数的比例
            outline: {
              borderDistance: 0,
              itemStyle: {
                borderWidth: 2,
                borderColor: "#3dfff6",
                shadowBlur: 20,
                shadowColor: "#12786f",
              },
            },
            color: ["rgba(50, 255, 238, .6)", "rgba(154, 255, 247, .6)"],
            backgroundStyle: {
              color: "transparent",
            },
            label: {
              show: true,
              textStyle: {
                color: "#12786f",
                insideColor: "#12786f",
                fontSize: 28,
              },
              formatter: (params) => {
                return `${(params.value * 100).toFixed(0)}%\n {a|男性}`;
              },
              rich: {
                a: {
                  fontSize: 20,
                },
              },
            },
          },
          {
            name: "女性",
            type: "liquidFill",
            shape: "circle",
            radius: "65%",
            center: ["70%", "50%"],
            data: [0.37], // 女性占总人数的比例
            outline: {
              borderDistance: 0,
              itemStyle: {
                borderWidth: 2,
                borderColor: "#ff4f4f",
                shadowBlur: 20,
                shadowColor: "#ff0000",
              },
            },
            color: ["rgba(255, 100, 100, .6)", "rgba(255, 200, 200, .6)"],
            backgroundStyle: {
              color: "transparent",
            },
            label: {
              show: true,
              textStyle: {
                color: "#ff0000",
                insideColor: "#ff0000",
                fontSize: 28,
              },
              formatter: (params) => {
                return `${(params.value * 100).toFixed(0)}%\n {b|女性}`;
              },
              rich: {
                b: {
                  fontSize: 20,
                },
              },
            },
          },
        ],
      };
      chart.setOption(option);
    },
    //词云图
    // initWorld() {
    //   this.chartInstance = echarts.init(this.$refs.wordCloud);
    //   this.updateWordCloud(this.wordCloudData2); // 使用初始数据进行初始化
    //   this.currentData = this.wordCloudData2; // 记录当前数据
    // },
    // randomRGB() {
    //   const r = Math.floor(Math.random() * 200);
    //   const g = Math.floor(Math.random() * 200);
    //   const b = Math.floor(Math.random() * 200);
    //   return `rgb(${r}, ${g}, ${b})`;
    // },
    // updateWordCloud(data) {
    //   if (this.chartInstance) {
    //     const option = {
    //       tooltip: {
    //         show: true,
    //       },
    //       series: [
    //         {
    //           type: "wordCloud",
    //           shape: "pentagon",
    //           sizeRange: [15, 60],
    //           rotationRange: [0, 0],
    //           rotationStep: 45,
    //           gridSize: 7,
    //           textStyle: {
    //             // 随机色值
    //             color: () => this.randomRGB(), // 这里使用箭头函数
    //           },
    //           data: data,
    //           emphasis: {
    //             itemStyle: {
    //               shadowBlur: 10,
    //               shadowOffsetX: 0,
    //               shadowColor: "rgba(0, 0, 0, 0.5)",
    //             },
    //           },
    //         },
    //       ],
    //     };
    //     this.chartInstance.setOption(option);
    //   }
    // },
    // startAutoUpdateWordCloud() {
    //   setInterval(() => {
    //     this.fetchData().then((data) => {
    //       if (data !== this.currentData) {
    //         // 仅在数据不同时才更新
    //         this.updateWordCloud(data);
    //         this.currentData = data; // 更新当前数据
    //       }
    //     });
    //   }, 800); // 每秒更新一次
    // },
    // fetchData() {
    //   return new Promise((resolve) => {
    //     setTimeout(() => {
    //       // 随机选择数据，避免重复
    //       const newData =
    //         Math.random() < 0.5 ? this.wordCloudData1 : this.wordCloudData2;
    //       resolve(newData);
    //     }, 1000); // 模拟网络延迟
    //   });
    // },
    // onResize() {
    //   if (this.chartInstance) {
    //     this.chartInstance.resize();
    //   }
    // },

    //折线图+柱状图
    initLinebar() {
      this.linebar = echarts.init(this.$refs.linebar);
      this.option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#283b56",
            },
          },
        },
        legend: {
          textStyle: {
            color: "#fff", // 图例文字颜色
          },
        },
        toolbox: {
          show: true,
          feature: {
            dataView: { readOnly: false },
            restore: {},
            saveAsImage: {},
          },
        },
        dataZoom: [
          {
            type: "slider",
            start: 40,
            end: 50,
          },
        ],
        xAxis: [
          {
            type: "category",
            boundaryGap: true,
            data: this.categories,
            axisLabel: {
              color: "#fff", // X轴文字颜色
            },
          },
          {
            type: "category",
            boundaryGap: true,
            data: this.categories2,
            axisLabel: {
              color: "#fff", // X轴文字颜色
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            scale: true,
            name: "出院人数",
            max: 400,
            min: 0,
            boundaryGap: [0.3, 0.3],
            axisLabel: {
              color: "#fff", // Y轴文字颜色
            },
          },
          {
            type: "value",
            scale: true,
            name: "入院人数",
            max: 400,
            min: 0,
            boundaryGap: [0.3, 0.3],
            axisLabel: {
              color: "#fff", // Y轴文字颜色
            },
          },
        ],
        series: [
          {
            name: "出院人数",
            type: "bar",
            xAxisIndex: 1,
            yAxisIndex: 1,
            data: this.date,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                1,
                0, // x1, y1, x2, y2（0-1）表示渐变开始和结束的位置，分别对应左上角和右下角
                [
                  { offset: 0, color: "#a8edea" }, // 0% 处的颜色
                  { offset: 1, color: "#fed6e3" }, // 100% 处的颜色
                ]
              ),
              borderRadius: 8, // 柱状图圆角
            },
            animationDelay: function (idx) {
              return Math.random() * 500;
            },
          },
          {
            name: "入院人数",
            type: "line",
            data: this.data2,
            smooth: true, // 折线图平滑
            symbol: "circle", // 标记点形状
            symbolSize: 8, // 标记点大小
            itemStyle: {
              color: "#ffffff", // 折线图颜色
            },
            lineStyle: {
              width: 2,
              // 折线颜色
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                1,
                0, // x1, y1, x2, y2（0-1）表示渐变开始和结束的位置，分别对应左上角和右下角
                [
                  { offset: 0, color: "#FF7E5F" }, // 0% 处的颜色
                  { offset: 1, color: "#73B3B2" }, // 100% 处的颜色
                ]
              ),
            },
            animationDelay: function (idx) {
              return Math.random() * 500;
            },
          },
        ],
      };
      this.linebar.setOption(this.option);
    },
    async fetchDynamicData() {
      try {
        const { default: dischargeData } = await import(
          "./data/discharge_data.json"
        );
        const { default: admissionData } = await import(
          "./data/admission_data.json"
        );

        this.categories = dischargeData.map((item) => item.discharge_date);
        this.date = dischargeData.map((item) => item.discharge_values);
        this.categories2 = admissionData.map((item) => item.admission_date);
        this.data2 = admissionData.map((item) => item.admission_values);

        this.linebar.setOption({
          xAxis: [{ data: this.categories }, { data: this.categories2 }],
          series: [{ data: this.date }, { data: this.data2 }],
        });
      } catch (error) {
        console.error("数据加载失败:", error);
      }
    },
    startDynamicUpdate() {
      setInterval(() => {
        const axisData = new Date().toLocaleTimeString().replace(/^\D*/, "");
        this.categories.shift();
        this.categories.push(axisData);
        this.categories2.shift();
        this.categories2.push(this.count++);

        // 更新数据
        this.date.shift();
        this.date.push(Math.round(Math.random() * 100));
        this.data2.shift();
        this.data2.push(Math.round(Math.random() * 10 + 5));

        this.linebar.setOption({
          xAxis: [{ data: this.categories }, { data: this.categories2 }],
          series: [{ data: this.date }, { data: this.data2 }],
        });
      }, 1000);
    },
  },
  // created() {
  //   this.loadData(); // 在组件创建时加载数据
  //   this.startScroll(); // 启动滚动
  // },
  data() {
    return {
      //各年龄段患病信息展示图
      data: data,
      selectedAgeGroup: "",
      selectedDiagnoses: [],
      //年龄结构图
      ageGap: [
        "65-69岁",
        "70-74岁",
        "75-79岁",
        "80-84岁",
        "85-89岁",
        "90-94岁",
        "95+岁",
      ],
      dataAge: [
        {
          year: "2021",
          age_data: {
            male: [29.87, 22.68, 24.83, 12.54, 6.54, 2.7, 0.7, 0.12],
            female: [28.86, 25.91, 21.13, 12.23, 7.41, 3.53, 0.83, 0.1],
          },
        },
        {
          year: "2022",
          age_data: {
            male: [29.99, 21.82, 25.24, 12.07, 6.75, 3.15, 0.83, 0.15],
            female: [29.41, 24.49, 20.58, 12.35, 7.54, 4.11, 1.39, 0.13],
          },
        },
      ],
      //玫瑰图数据
      chartDom: null,
      roseChartData: require("./data/roseChartData.json"),
      //词云图数据
      // wordCloudData1: require("./data/wordCloudData1.json"),
      // wordCloudData2: require("./data/wordCloudData2.json"),
      // currentData: null,
      // lastUpdateTime: 0,
      //桑基图数据

      // 表格数据
      // intervalId: null, // 定时器ID
      // 初始化就诊人数数据
      // currentIndex: 0,
      // rowHeight: 50, // 每行的高度
      // headers: ["患者编码", "主诊断名称", "其他病症1", "其他病症2"],
      // data: [],

      //柱状图+折线图
      mylinebar: null,
      categories: [],
      categories2: [],
      date: [],
      data2: [],
      count: 0, // 用于动态生成 x 轴的索引
      maxDataPoints: 10, // 每次展示的数据点数量
    };
  },
};
</script>

<style>
.nav-link1 {
  width: 230px;
  color: #8da7f5;
  font-size: 20px;
  margin-right: 90px;
  font-weight: bold;
}
.nav-link2 {
  width: 230px;
  color: #8da7f5;
  font-size: 20px;
  margin-left: 90px;
  font-weight: bold;
}

@import "css.css";
</style>
