<template>
  <el-scrollbar height="600px">
    <el-row class="home" :gutter="20">
      <!-- 占5列 -->
      <!-- z左侧列 -->
      <el-col :span="7" style="margin-top: 10px">
        <el-card shadow="hover" style="height: 450px; border-radius: 15px">
          <div class="info-box" style="height: 370px; width: 320px">
            <div class="info-box-content">
              <div style="text-align: center; margin-top: 5px">
                <el-tag
                  type="success"
                  style="font-size: 25px; color: #8da7f5; font-weight: bold"
                  >体检信息解读</el-tag
                >
              </div>
              <!-- 病人信息展示区 -->
              <div>
                <div class="search-bar">
                  <input
                    type="text"
                    v-model="searchId"
                    placeholder="请输入您的就诊ID"
                    @keydown.enter="filterDataAndClear"
                  />
                </div>

                <div v-if="filteredData" class="card">
                  <h3 style="color: #fff">检查项信息</h3>
                  <p style="color: #fff">
                    检查项名称：{{ filteredData.检查项名称 }}
                  </p>
                  <p style="color: #fff">
                    器官名称：{{ filteredData.器官名称 }}
                  </p>
                  <button class="styled-button" @click="showDetails = true">
                    生成AI解析
                  </button>
                </div>
                <div v-if="showDetails" class="modal" v-show="false">
                  <div class="modal-content">
                    <span class="close" @click="showDetails = false"
                      >&times;</span
                    >
                    <div>
                      <div class="modal-body" style="color: #fff">
                        <p>AI解析：</p>
                        <div class="check-details">
                          <ChatAI
                            v-if="filteredData"
                            :inspection-content="getInspectionContent()"
                            @ai-response="handleAIResponse"
                          />
                        </div>
                        <div>
                          <ChatAI
                            v-if="filteredData"
                            :inspection-content="getInspectionOtherContent()"
                            @ai-other-response="handleAIotherResponse"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div style="margin-left: 100px; margin-top: 5px">
            <button class="button" @click="routerChatAI()">向AI咨询</button>
          </div>
        </el-card>
        <el-card
          shadow="hover"
          style="margin-top: 20px; height: 320px; border-radius: 15px"
        >
          <div style="text-align: center; margin-bottom: 20px">
            <el-tag
              type="success"
              style="font-size: 15px; color: #8da7f5; font-weight: bold"
            >
              患病器官展示
            </el-tag>
          </div>
          <div v-if="filteredData" class="organ-display">
            <img
              :src="organImagePath"
              :alt="filteredData.器官名称"
              class="organ-image"
            />
            <div class="organ-name">
              {{ filteredData.器官名称 }}
            </div>
          </div>
          <div v-else class="no-data-tip">请输入就诊ID查看相关器官图片</div>
        </el-card>
      </el-col>
      <!-- 右侧列 -->
      <el-col :span="17" style="margin-top: 10px">
        <el-card style="height: 450px; border-radius: 15px" shadow="hover">
          <!-- 人体模型图 + 人体四部分患病占比图-->
          <div style="display: flex">
            <div style="text-align: center; margin-top: 5px; flex: auto">
              <el-tag
                type="success"
                style="font-size: 20px; color: #8da7f5; font-weight: bold"
              >
                体检信息可视化</el-tag
              >
            </div>
            <div class="search-box">
              <input
                v-model="searchCode"
                placeholder="输入 ID"
                @input="fetchData"
                type="text"
              />
            </div>
          </div>

          <div style="display: flex">
            <!-- 人体模型图 -->
            <div ref="chart" style="height: 420px; width: 350px"></div>
            <!-- 热力图 -->

            <div ref="heatmap" style="height: 340px; width: 650px"></div>
          </div>
        </el-card>
        <div class="graph">
          <el-card style="height: 300px; border-radius: 15px" shadow="hover">
            <!-- 诊断分析 -->
            <el-tag
              type="success"
              style="font-size: 15px; color: #8da7f5; font-weight: bold"
            >
              诊断分析</el-tag
            >
            <div style="margin-top: 5px">
              <el-card style="height: 100%; width: 100%; margin-left: 5px">
                <div v-html="sparkResult" style="color: #fff"></div>
              </el-card>
            </div>
          </el-card>
          <el-card style="height: 300px; border-radius: 15px" shadow="hover">
            <!-- 生活习惯等建议-->
            <el-tag
              type="success"
              style="font-size: 15px; color: #8da7f5; font-weight: bold"
            >
              生活习惯等建议</el-tag
            >
            <div style="margin-top: 5px">
              <el-card style="height: 100%; width: 100%; margin-left: 5px">
                <div v-html="sparkOtherResult" style="color: #fff"></div>
              </el-card>
            </div>
          </el-card>
        </div>
      </el-col>
    </el-row>
  </el-scrollbar>
</template>
<script>
//导入AI大模型页面
import ChatAI from "../ChatAI/ChatAI.vue";
import data_new from "./data/new_data.json";
import { Notification } from "element-ui";
import DrawBorderButton from "./DrawBorderButton.vue";
import * as echarts from "echarts";
import exm_data from "./data/examination_data.json";
import page from "./"
export default {
  components: {
    ChatAI,
  },
  data() {
    return {
      //AI解析
      inspectionContent: "",
      sparkResult: "",
      sparkOtherResult: "",
      //信息展示区
      searchId: "",
      showDetails: false,
      data: data_new,
      filteredData: null,

      //热力图
      searchCode: "",
      heatMapData: [],
      //人体器官图
      organImagePath: "", // 添加新的数据属性
      organToBodyPartMap: {
        "脑": "头部",
        "心": "胸部",
        "肺": "胸部",
        "肝脏": "腹部",
        "胃": "腹部",
        "肾脏": "腹部",
        "肝胆":"腹部",
        "腿":"腿"
        // 可以继续添加其他器官与身体部位的映射
      },
      highlightedPart: null, // 用于存储当前需要高亮的部位
    };
  },
  methods: {
    //跳转AI问答页面
    routerChatAI() {
      this.$router.push("/ChatAI");
    },
    //AI解析区
    getInspectionContent() {
      return (
        "概括关键点，并给出相应的就诊建议以及可能引起的并发症，内容需简洁，不超过200字,不超过200字,不超过200字 \n" + this.filteredData["检查所见"]
      );
    },
    getInspectionOtherContent() {
      return (
        "提供关于饮食和生活习惯具体精准的建议，内容需简洁，不超过200字, 不超过200字, 不超过200字\n" +
        this.filteredData["检查所见"]
      );
    },
    // handleUpdateSparkResult(result) {
    //   this.sparkResult = result;
    // },

    //病人信息展示
    filterDataAndClear() {
      this.sparkResult = "";
      this.sparkOtherResult = "";
      this.filterData();
    },
    filterData() {
      this.filteredData = this.data.find(
        (item) => item["就诊编码"] === this.searchId
      );
      if (this.filteredData) {
        this.openNotification("success", "成功", "成功找到匹配的检查信息！");
        this.updateOrganImage();
        this.updateHighlightedPart(); // 添加这行来更新高亮部位
      } else {
        this.openNotification(
          "error",
          "错误",
          "未匹配到正确的检查信息，请输入正确的ID。"
        );
        this.organImagePath = "";
        this.highlightedPart = null;
        this.updateChart();
      }
    },
    //开启AI解读
    handleAIResponse(response) {
      this.sparkResult = response;
    },
    handleAIotherResponse(otheresponse) {
      this.sparkOtherResult = otheresponse;
    },
    openNotification(type, title, message) {
      Notification({
        title: title,
        message: message,
        type: type,
        duration: 3000,
      });
    },

    //热力图
    fetchData() {
      if (this.searchCode) {
        const filteredData = exm_data.filter(
          (item) => item.code === this.searchCode
        );
        if (filteredData.length) {
          this.heatMapData = filteredData.map((item) => ({
            item: item.checkitem,
            result: parseFloat(item.result),
            normalRange: item.range,
            unit: item.unit,
          }));
          this.drawHeatmap();
        } else {
          this.heatMapData = []; // 清空数据
        }
      } else {
        this.heatMapData = []; // 清空数据
      }
    },
    drawHeatmap() {
      if (this.heatMapData.length === 0) {
        this.searchCode = "1001Z810000000YR5SLW";
        this.fetchData();
      }
      const chartDom = this.$refs.heatmap;
      const myChart = echarts.init(chartDom);

      const data = this.heatMapData.map((item) => {
        const [minRange, maxRange] = item.normalRange.split(" - ").map(Number);
        let value = 0;

        // 比较结果与正常范围
        if (item.result < minRange) {
          value = Math.max(
            0,
            (minRange - item.result) / (minRange - minRange * 0.5)
          ); // 偏低
        } else if (item.result > maxRange) {
          value = Math.min(
            1,
            (item.result - maxRange) / (maxRange * 1.5 - maxRange)
          ); // 偏高
        }

        return [item.item, "结果", value];
      });

      const option = {
        tooltip: {
          show: true,
          position: "top",
          formatter: (params) => {
            const item = this.heatMapData.find(
              (test) => test.item === params.name
            );
            const [minRange, maxRange] = item.normalRange
              .split(" - ")
              .map(Number);
            let status = "正常";

            if (item.result < minRange) {
              status = `偏低 (${item.result} < ${minRange})`;
            } else if (item.result > maxRange) {
              status = `偏高 (${item.result} > ${maxRange})`;
            } else {
              status = `正常 (${maxRange} > ${item.result} > ${minRange})`;
            }

            return `${params.name}<br/>结果: ${item.result}<br/>${status}`;
          },
        },
        xAxis: {
          type: "category",
          data: this.heatMapData.map((item) => item.item),
          axisLabel: {
            color: "#fff",
          },
        },
        yAxis: {
          type: "category",
          data: ["结果"],
          axisLabel: {
            color: "#fff",
          },
        },
        visualMap: {
          min: 0,
          max: 1,
          calculable: true,
          inRange: {
            color: ["#FFFFFF", "#FF0000"], // 从白色到红色的渐变
          },
          outOfRange: {
            color: "#BEBEBE",
          },
        },
        grid: {
          top: "5%",
          bottom: "10%",
        },
        series: [
          {
            type: "heatmap",
            data: data,
            label: {
              show: true,
              formatter: "{b}",
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowColor: "#333",
              },
            },
          },
        ],
        dataZoom: [
          {
            type: "slider",
            xAxisIndex: 0,
            start: 20,
            end: 60,
            bottom: "1%",
            handleSize: "100%",
            handleStyle: {
              color: "#fff",
            },
          },
        ],
      };

      myChart.setOption(option);
    },

    //人体器官图
    updateChart() {
      var symbols = [
        "path://M90.652,31H90C90,31,90.179,31.368,90.652,31z M152,38c-0.584-3.417-1.834-9.667-3-11				c0.916,0.083,3-1,3-1s-1.084-1.333-3-2s-7.334-7.333-10-10s-8-4-8-4s0.311-0.771,1-1c0.802-0.268,2,0,2,0s-1.584-1-4-1				c-1.609,0-2.847,1.771-3.495,2.957C125.859,9.718,124.62,7.727,123,7c-2.416-1.083-3-1-3-1s1.75,2.083,2,3c0,0-1.5-1.167-5-1				c-1.497,0.071-2.445,0.265-3.04,0.462C113.164,8.727,113,9,113,9h1c0,0-4.333,0.417-8,2s-9,6-9,6h2c0,0-3.167,4.083-4,5				c-2.167,0.333-2,0-2,0s-0.417,0.417,1,1s1,1,1,1s-1.667,2.333-3,5c-0.6,1.2-1.037,1.758-1.348,2H92c0,0-0.004,1.607-1,5				c-0.714,2.434-0.287,8.385,0,9c0,0,0.75-4.083,1-3s-0.167,1.583,0,4s1,3,1,3l1-2c0,0,0.917,4.5,1,6s0,2,0,2l1-2v1l0.569-0.854				c0.529,0.498,1.135,0.37,1.635,0.552c-1.379,3.658,1.457,7.579,3.234,10.255c1.909,2.873,3.84,2.011,3.84,2.011l-0.079-0.541				c0.178,0.653,0.354,1.271,0.523,1.804c2.219,6.977,3.079,8.363,7.593,12.147c3.386,2.838,3.315,4.626,8.783,4.626				c5.669,0,5.154-1.94,8.896-4.974c3.35-2.716,5.586-4.172,7.426-10.634c0.188-0.662,0.404-1.426,0.631-2.23				c0.561,0.123,2.009,0.171,3.472-2.062c1.644-2.51,4.214-6.127,3.333-9.58c0.46-0.254,1.039-0.327,1.322-1.365				c0.053-0.192,0.109-0.4,0.164-0.602C148.352,51.021,150,47,150,47v1.754c0.115-0.262,0.385-0.763,1-1.754c1.5-2.417,0-7,0-7l1,2				C152,42,152.584,41.417,152,38z M122.072,9.264C122.137,9.5,122.109,9.402,122.072,9.264L122.072,9.264z",
        "path://M134.9,85.032c-0.596-0.533-0.982-4.968-1.021-8.421c-0.864,0.812-1.817,1.55-2.885,2.415				C127.253,82.06,127.768,84,122.099,84c-5.468,0-5.397-1.788-8.783-4.626c-1.129-0.946-2.029-1.742-2.779-2.514				c-0.249,3.256-0.752,7.572-1.437,8.172l-10.432,6.291c3.911,2.136,11.363,5.01,23.582,5.427				c14.092,0.48,20.283-3.155,23.198-5.335C137.705,87.858,134.9,85.032,134.9,85.032z",
        "path://M88.01,126.492c0,0,0.01,0.076,0.023,0.171C88.019,126.561,88.01,126.492,88.01,126.492z				 M87.75,94.917c-0.019-0.057-0.071-0.157-0.149-0.29c1.097-0.092-2.202-0.059-5.199,0.948				c-4.025,1.417-8.266,5.413-11.037,12.738c-1.035,2.736-1.51,5.936-1.761,9.639c0,2.919-0.023,6.811-1.866,13.621				c-1.843,3.892-5.529,15.567-6.45,24.323c-0.921,3.892-4.607,11.676-5.528,15.567c-0.921,2.919-7.19,29.682-10.778,52.059				c0,3.892-7.395,7.783-8.316,9.729s-2.921,7.507-5.186,10.257c-0.319,0.387-0.823,2.312,0.518,2.218				c1.122-0.079,3.91-1.945,5.589-3.718c0,0-2.364,11.675-2.364,15.566c0,0,0.521,4.864,2.364,0l2.764-10.702				c0,0,0.921-0.973,0,7.783c0,2.919-1.321,10.702,2.364,5.838c0,0,2.243-9.729,2.243-12.648c0,0,0.922-1.945,0.922,2.919				c0,0-0.922,11.675,1.843,10.702c0.921-0.973,0.921-9.729,1.843-13.621c0,0,0.921-1.945,0.921,2.919				c0,2.919,1.8,12.275,2.764,3.892c-0.079-2.746,1.164-8.423,1.843-25.936c0-2.919,1.25-7.7,4.936-13.538				c1.843-2.919,13.136-30.098,13.136-39.827c0-3.892,14.844-39.134,14.844-48.863C86.609,118.001,91.359,105.743,87.75,94.917z				 M212.27,244.259c-1.764-1.75-5.014-9.061-5.936-11.007s-8.315-5.838-8.315-9.729c-2.255-21.044-8.857-49.14-9.778-52.059				c-0.922-3.892-4.607-11.676-5.529-15.567c-0.921-8.756-4.606-20.432-6.449-24.323c-1.843-6.811-1.866-10.702-1.866-13.621				c-0.229-3.394-0.608-6.364-1.508-8.942c-2.712-7.77-7.14-11.974-11.29-13.435c-1.994-0.67-3.841-0.987-5.571-1.227				c-0.298,0.751-0.671,1.629-0.999,2.498c0.086-0.187-0.565,2.076-0.15,7.18c0.543,6.646,2.213,17.229,1.113,22.466				c0,9.729,14.845,44.972,14.845,48.863c0,9.729,11.876,40.158,13.719,43.077c3.686,5.838,3.686,10.702,3.686,13.621				c1.278,17.513,0.588,19.057,1.509,22.603c1.765,7.983,2.765-0.973,2.765-3.892c0-4.864,0.922-2.919,0.922-2.919				c0.921,3.892,1.121,12.648,2.042,13.621c2.765,0.973,1.644-10.702,1.644-10.702c0-4.864,0.921-2.919,0.921-2.919				c0,2.919,2.243,12.648,2.243,12.648c3.686,4.864,2.364-2.919,2.364-5.838c-0.922-8.756,0-7.783,0-7.783l2.764,10.702				c1.844,4.864,2.164,0,2.164,0c0-3.892-2.164-15.566-2.164-15.566c1.129,1.191,2.521,3.425,3.543,3.851				C211.271,246.824,212.724,244.709,212.27,244.259z",
        "path://M154.877,104.026c-0.415-5.103,0.236-7.366,0.15-7.18c0.328-0.87,0.702-1.748,1-2.499				c-2.564-0.354-4.857-0.523-6.89-1.344c-1.339-0.541-2.565-1.072-3.689-1.589c-2.915,2.18-9.106,5.815-23.198,5.335				c-12.219-0.417-19.671-3.291-23.582-5.427c-1.649,0.771-3.513,1.578-5.637,2.408c-1.654,0.647-3.465,0.73-5.43,0.896				c0.078,0.133,0.13,0.233,0.149,0.29c3.609,10.826-1.141,23.084,0.26,31.575c0,0,2.371,17.832,4.741,28.982				c4.221-1.504,10.153-3.523,14.706-4.374c4.68-0.875,9.983-1.627,15.293-1.351c7.18,0.373,13.266,1.431,17.996,2.335				c3.427,0.655,7.604,1.335,10.58,1.815c1.916-11.175,4.664-27.408,4.664-27.408C157.09,121.256,155.42,110.673,154.877,104.026z",
        "path://M155.328,193.896c-0.922-6.811-6.666-24.579-4.866-34.979c0.254-1.468,0.549-3.182,0.864-5.017				c-2.976-0.48-7.153-1.16-10.58-1.815c-4.73-0.904-10.816-1.962-17.996-2.335c-5.31-0.276-10.613,0.476-15.293,1.351				c-10.072,1.882-14.755,4.496-14.724,4.394c-0.013,0.038,0.494,2.179,0.805,3.423c2.6,10.399-3.944,28.168-4.866,34.979				c-0.088,0.279-0.201,0.859-0.332,1.688c3.913,1.58,16.325,5.753,36.16,4.666c17.666-0.968,27.332-2.77,31.307-3.685				C155.611,195.203,155.447,194.274,155.328,193.896z",
        "path://M159.268,211.039c-0.441-4.467-1.216-8.439-1.735-10.794c0.19-0.086,0.356-0.169,0.468-0.245				c0.864-0.588,0-4,0-4s-9.166,2.917-33.5,4.25C100.167,201.583,87,195,87,195s-0.667,1.667-1,3				c-0.054,0.214,0.171,0.455,0.607,0.713c-0.681,3.063-2.089,9.854-2.922,17.071c-1.336,11.574-0.92,17.69-0.185,18.216				c2.333,1.667,36.167,4.75,37.166,2.75c1-2,1-2,1-2l2.043-0.167L124,235.75c0,0,0.666,3,12,2s24.804-2.678,24.75-5.75				C160.689,228.563,160.285,221.326,159.268,211.039z",
        "path://M154.25,321.191c-1.844-5.838-0.922-16.54,0-20.432c3.686-15.567,5.527-32.976,5.527-46.597				c0-6.154-0.268-13.261-0.674-20.391c-3.922,1.98-14.174,3.189-23.104,3.978c-8.324,0.734-10.892-0.688-11.67-1.496				c1.333,19.9,4.12,30.385,4.12,37.155c0,21.404,5.528,44.863,5.528,53.619c0,8.757-6.084,21.297-2.741,37.161				c3.686,8.757,2.741,49.075,2.741,50.048c-0.922,6.811-2.764,8.757-2.764,10.703c0,1.945,2.764,4.864,0.921,7.783				s0,7.783,1.843,9.729c1.843,1.945,5.528-1.946,13.82-0.974c8.293,0.974,5.529-3.892,5.529-3.892				c-3.686-0.973-5.529-5.837-5.529-5.837s0.922-6.811,0-8.757c-2.764-8.757,2.766-51.021,4.607-55.886				C157.936,354.46,156.092,328.002,154.25,321.191z M84.857,234.48c-0.384,6.892-0.635,13.732-0.635,19.683				c0,13.621,1.843,31.029,5.529,46.597c0.921,3.892,1.843,14.594,0,20.432c-1.843,6.811-3.686,33.269,1.843,45.917				c1.843,4.864,7.371,47.129,4.607,55.886c-0.921,1.946,0,8.757,0,8.757s-1.843,4.864-5.528,5.837c0,0-2.765,4.865,5.528,3.892				c8.292-0.973,11.978,2.919,13.821,0.974c1.843-1.946,3.686-6.811,1.843-9.729c-1.843-2.919,0.921-5.838,0.921-7.783				c0-1.946-1.843-3.893-2.765-10.703c0-0.973-0.944-41.291,2.742-50.048c1.843-17.364-2.742-28.404-2.742-37.161				c0-8.756,5.529-32.215,5.529-53.619c0-6.662,2.696-16.941,4.052-36.231C114.286,238.093,90.977,236.009,84.857,234.48z",
        "path://M90.652,31H90C90,31,90.179,31.368,90.652,31z M152,38c-0.584-3.417-1.834-9.667-3-11				c0.916,0.083,3-1,3-1s-1.084-1.333-3-2s-7.334-7.333-10-10s-8-4-8-4s0.311-0.771,1-1c0.802-0.268,2,0,2,0s-1.584-1-4-1				c-1.609,0-2.847,1.771-3.495,2.957C125.859,9.718,124.62,7.727,123,7c-2.416-1.083-3-1-3-1s1.75,2.083,2,3c0,0-1.5-1.167-5-1				c-1.497,0.071-2.445,0.265-3.04,0.462C113.164,8.727,113,9,113,9h1c0,0-4.333,0.417-8,2s-9,6-9,6h2c0,0-3.167,4.083-4,5				c-2.167,0.333-2,0-2,0s-0.417,0.417,1,1s1,1,1,1s-1.667,2.333-3,5c-0.6,1.2-1.037,1.758-1.348,2H92c0,0-0.004,1.607-1,5				c-0.714,2.434-0.287,8.385,0,9c0,0,0.75-4.083,1-3s-0.167,1.583,0,4s1,3,1,3l1-2c0,0,0.917,4.5,1,6s0,2,0,2l1-2v1l0.569-0.854				c0.529,0.498,1.135,0.37,1.635,0.552c-1.379,3.658,1.457,7.579,3.234,10.255c1.909,2.873,3.84,2.011,3.84,2.011l-0.079-0.541				c0.178,0.653,0.354,1.271,0.523,1.804c2.219,6.977,3.079,8.363,7.593,12.147c3.386,2.838,3.315,4.626,8.783,4.626				c5.669,0,5.154-1.94,8.896-4.974c3.35-2.716,5.586-4.172,7.426-10.634c0.188-0.662,0.404-1.426,0.631-2.23				c0.561,0.123,2.009,0.171,3.472-2.062c1.644-2.51,4.214-6.127,3.333-9.58c0.46-0.254,1.039-0.327,1.322-1.365				c0.053-0.192,0.109-0.4,0.164-0.602C148.352,51.021,150,47,150,47v1.754c0.115-0.262,0.385-0.763,1-1.754c1.5-2.417,0-7,0-7l1,2				C152,42,152.584,41.417,152,38z M122.072,9.264C122.137,9.5,122.109,9.402,122.072,9.264L122.072,9.264z",
        "path://M134.9,85.032c-0.596-0.533-0.982-4.968-1.021-8.421c-0.864,0.812-1.817,1.55-2.885,2.415				C127.253,82.06,127.768,84,122.099,84c-5.468,0-5.397-1.788-8.783-4.626c-1.129-0.946-2.029-1.742-2.779-2.514				c-0.249,3.256-0.752,7.572-1.437,8.172l-10.432,6.291c3.911,2.136,11.363,5.01,23.582,5.427				c14.092,0.48,20.283-3.155,23.198-5.335C137.705,87.858,134.9,85.032,134.9,85.032z",
        "path://M88.01,126.492c0,0,0.01,0.076,0.023,0.171C88.019,126.561,88.01,126.492,88.01,126.492z				 M87.75,94.917c-0.019-0.057-0.071-0.157-0.149-0.29c1.097-0.092-2.202-0.059-5.199,0.948				c-4.025,1.417-8.266,5.413-11.037,12.738c-1.035,2.736-1.51,5.936-1.761,9.639c0,2.919-0.023,6.811-1.866,13.621				c-1.843,3.892-5.529,15.567-6.45,24.323c-0.921,3.892-4.607,11.676-5.528,15.567c-0.921,2.919-7.19,29.682-10.778,52.059				c0,3.892-7.395,7.783-8.316,9.729s-2.921,7.507-5.186,10.257c-0.319,0.387-0.823,2.312,0.518,2.218				c1.122-0.079,3.91-1.945,5.589-3.718c0,0-2.364,11.675-2.364,15.566c0,0,0.521,4.864,2.364,0l2.764-10.702				c0,0,0.921-0.973,0,7.783c0,2.919-1.321,10.702,2.364,5.838c0,0,2.243-9.729,2.243-12.648c0,0,0.922-1.945,0.922,2.919				c0,0-0.922,11.675,1.843,10.702c0.921-0.973,0.921-9.729,1.843-13.621c0,0,0.921-1.945,0.921,2.919				c0,2.919,1.8,12.275,2.764,3.892c-0.079-2.746,1.164-8.423,1.843-25.936c0-2.919,1.25-7.7,4.936-13.538				c1.843-2.919,13.136-30.098,13.136-39.827c0-3.892,14.844-39.134,14.844-48.863C86.609,118.001,91.359,105.743,87.75,94.917z				 M212.27,244.259c-1.764-1.75-5.014-9.061-5.936-11.007s-8.315-5.838-8.315-9.729c-2.255-21.044-8.857-49.14-9.778-52.059				c-0.922-3.892-4.607-11.676-5.529-15.567c-0.921-8.756-4.606-20.432-6.449-24.323c-1.843-6.811-1.866-10.702-1.866-13.621				c-0.229-3.394-0.608-6.364-1.508-8.942c-2.712-7.77-7.14-11.974-11.29-13.435c-1.994-0.67-3.841-0.987-5.571-1.227				c-0.298,0.751-0.671,1.629-0.999,2.498c0.086-0.187-0.565,2.076-0.15,7.18c0.543,6.646,2.213,17.229,1.113,22.466				c0,9.729,14.845,44.972,14.845,48.863c0,9.729,11.876,40.158,13.719,43.077c3.686,5.838,3.686,10.702,3.686,13.621				c1.278,17.513,0.588,19.057,1.509,22.603c1.765,7.983,2.765-0.973,2.765-3.892c0-4.864,0.922-2.919,0.922-2.919				c0.921,3.892,1.121,12.648,2.042,13.621c2.765,0.973,1.644-10.702,1.644-10.702c0-4.864,0.921-2.919,0.921-2.919				c0,2.919,2.243,12.648,2.243,12.648c3.686,4.864,2.364-2.919,2.364-5.838c-0.922-8.756,0-7.783,0-7.783l2.764,10.702				c1.844,4.864,2.164,0,2.164,0c0-3.892-2.164-15.566-2.164-15.566c1.129,1.191,2.521,3.425,3.543,3.851				C211.271,246.824,212.724,244.709,212.27,244.259z",
        "path://M154.877,104.026c-0.415-5.103,0.236-7.366,0.15-7.18c0.328-0.87,0.702-1.748,1-2.499				c-2.564-0.354-4.857-0.523-6.89-1.344c-1.339-0.541-2.565-1.072-3.689-1.589c-2.915,2.18-9.106,5.815-23.198,5.335				c-12.219-0.417-19.671-3.291-23.582-5.427c-1.649,0.771-3.513,1.578-5.637,2.408c-1.654,0.647-3.465,0.73-5.43,0.896				c0.078,0.133,0.13,0.233,0.149,0.29c3.609,10.826-1.141,23.084,0.26,31.575c0,0,2.371,17.832,4.741,28.982				c4.221-1.504,10.153-3.523,14.706-4.374c4.68-0.875,9.983-1.627,15.293-1.351c7.18,0.373,13.266,1.431,17.996,2.335				c3.427,0.655,7.604,1.335,10.58,1.815c1.916-11.175,4.664-27.408,4.664-27.408C157.09,121.256,155.42,110.673,154.877,104.026z",
        "path://M155.328,193.896c-0.922-6.811-6.666-24.579-4.866-34.979c0.254-1.468,0.549-3.182,0.864-5.017				c-2.976-0.48-7.153-1.16-10.58-1.815c-4.73-0.904-10.816-1.962-17.996-2.335c-5.31-0.276-10.613,0.476-15.293,1.351				c-10.072,1.882-14.755,4.496-14.724,4.394c-0.013,0.038,0.494,2.179,0.805,3.423c2.6,10.399-3.944,28.168-4.866,34.979				c-0.088,0.279-0.201,0.859-0.332,1.688c3.913,1.58,16.325,5.753,36.16,4.666c17.666-0.968,27.332-2.77,31.307-3.685				C155.611,195.203,155.447,194.274,155.328,193.896z",
        "path://M159.268,211.039c-0.441-4.467-1.216-8.439-1.735-10.794c0.19-0.086,0.356-0.169,0.468-0.245				c0.864-0.588,0-4,0-4s-9.166,2.917-33.5,4.25C100.167,201.583,87,195,87,195s-0.667,1.667-1,3				c-0.054,0.214,0.171,0.455,0.607,0.713c-0.681,3.063-2.089,9.854-2.922,17.071c-1.336,11.574-0.92,17.69-0.185,18.216				c2.333,1.667,36.167,4.75,37.166,2.75c1-2,1-2,1-2l2.043-0.167L124,235.75c0,0,0.666,3,12,2s24.804-2.678,24.75-5.75				C160.689,228.563,160.285,221.326,159.268,211.039z",
        "path://M154.25,321.191c-1.844-5.838-0.922-16.54,0-20.432c3.686-15.567,5.527-32.976,5.527-46.597				c0-6.154-0.268-13.261-0.674-20.391c-3.922,1.98-14.174,3.189-23.104,3.978c-8.324,0.734-10.892-0.688-11.67-1.496				c1.333,19.9,4.12,30.385,4.12,37.155c0,21.404,5.528,44.863,5.528,53.619c0,8.757-6.084,21.297-2.741,37.161				c3.686,8.757,2.741,49.075,2.741,50.048c-0.922,6.811-2.764,8.757-2.764,10.703c0,1.945,2.764,4.864,0.921,7.783				s0,7.783,1.843,9.729c1.843,1.945,5.528-1.946,13.82-0.974c8.293,0.974,5.529-3.892,5.529-3.892				c-3.686-0.973-5.529-5.837-5.529-5.837s0.922-6.811,0-8.757c-2.764-8.757,2.766-51.021,4.607-55.886				C157.936,354.46,156.092,328.002,154.25,321.191z M84.857,234.48c-0.384,6.892-0.635,13.732-0.635,19.683				c0,13.621,1.843,31.029,5.529,46.597c0.921,3.892,1.843,14.594,0,20.432c-1.843,6.811-3.686,33.269,1.843,45.917				c1.843,4.864,7.371,47.129,4.607,55.886c-0.921,1.946,0,8.757,0,8.757s-1.843,4.864-5.528,5.837c0,0-2.765,4.865,5.528,3.892				c8.292-0.973,11.978,2.919,13.821,0.974c1.843-1.946,3.686-6.811,1.843-9.729c-1.843-2.919,0.921-5.838,0.921-7.783				c0-1.946-1.843-3.893-2.765-10.703c0-0.973-0.944-41.291,2.742-50.048c1.843-17.364-2.742-28.404-2.742-37.161				c0-8.756,5.529-32.215,5.529-53.619c0-6.662,2.696-16.941,4.052-36.231C114.286,238.093,90.977,236.009,84.857,234.48z",
        "path://M90.652,31H90C90,31,90.179,31.368,90.652,31z M152,38c-0.584-3.417-1.834-9.667-3-11				c0.916,0.083,3-1,3-1s-1.084-1.333-3-2s-7.334-7.333-10-10s-8-4-8-4s0.311-0.771,1-1c0.802-0.268,2,0,2,0s-1.584-1-4-1				c-1.609,0-2.847,1.771-3.495,2.957C125.859,9.718,124.62,7.727,123,7c-2.416-1.083-3-1-3-1s1.75,2.083,2,3c0,0-1.5-1.167-5-1				c-1.497,0.071-2.445,0.265-3.04,0.462C113.164,8.727,113,9,113,9h1c0,0-4.333,0.417-8,2s-9,6-9,6h2c0,0-3.167,4.083-4,5				c-2.167,0.333-2,0-2,0s-0.417,0.417,1,1s1,1,1,1s-1.667,2.333-3,5c-0.6,1.2-1.037,1.758-1.348,2H92c0,0-0.004,1.607-1,5				c-0.714,2.434-0.287,8.385,0,9c0,0,0.75-4.083,1-3s-0.167,1.583,0,4s1,3,1,3l1-2c0,0,0.917,4.5,1,6s0,2,0,2l1-2v1l0.569-0.854				c0.529,0.498,1.135,0.37,1.635,0.552c-1.379,3.658,1.457,7.579,3.234,10.255c1.909,2.873,3.84,2.011,3.84,2.011l-0.079-0.541				c0.178,0.653,0.354,1.271,0.523,1.804c2.219,6.977,3.079,8.363,7.593,12.147c3.386,2.838,3.315,4.626,8.783,4.626				c5.669,0,5.154-1.94,8.896-4.974c3.35-2.716,5.586-4.172,7.426-10.634c0.188-0.662,0.404-1.426,0.631-2.23				c0.561,0.123,2.009,0.171,3.472-2.062c1.644-2.51,4.214-6.127,3.333-9.58c0.46-0.254,1.039-0.327,1.322-1.365				c0.053-0.192,0.109-0.4,0.164-0.602C148.352,51.021,150,47,150,47v1.754c0.115-0.262,0.385-0.763,1-1.754c1.5-2.417,0-7,0-7l1,2				C152,42,152.584,41.417,152,38z M122.072,9.264C122.137,9.5,122.109,9.402,122.072,9.264L122.072,9.264z",
        "path://M134.9,85.032c-0.596-0.533-0.982-4.968-1.021-8.421c-0.864,0.812-1.817,1.55-2.885,2.415				C127.253,82.06,127.768,84,122.099,84c-5.468,0-5.397-1.788-8.783-4.626c-1.129-0.946-2.029-1.742-2.779-2.514				c-0.249,3.256-0.752,7.572-1.437,8.172l-10.432,6.291c3.911,2.136,11.363,5.01,23.582,5.427				c14.092,0.48,20.283-3.155,23.198-5.335C137.705,87.858,134.9,85.032,134.9,85.032z",
        "path://M88.01,126.492c0,0,0.01,0.076,0.023,0.171C88.019,126.561,88.01,126.492,88.01,126.492z				 M87.75,94.917c-0.019-0.057-0.071-0.157-0.149-0.29c1.097-0.092-2.202-0.059-5.199,0.948				c-4.025,1.417-8.266,5.413-11.037,12.738c-1.035,2.736-1.51,5.936-1.761,9.639c0,2.919-0.023,6.811-1.866,13.621				c-1.843,3.892-5.529,15.567-6.45,24.323c-0.921,3.892-4.607,11.676-5.528,15.567c-0.921,2.919-7.19,29.682-10.778,52.059				c0,3.892-7.395,7.783-8.316,9.729s-2.921,7.507-5.186,10.257c-0.319,0.387-0.823,2.312,0.518,2.218				c1.122-0.079,3.91-1.945,5.589-3.718c0,0-2.364,11.675-2.364,15.566c0,0,0.521,4.864,2.364,0l2.764-10.702				c0,0,0.921-0.973,0,7.783c0,2.919-1.321,10.702,2.364,5.838c0,0,2.243-9.729,2.243-12.648c0,0,0.922-1.945,0.922,2.919				c0,0-0.922,11.675,1.843,10.702c0.921-0.973,0.921-9.729,1.843-13.621c0,0,0.921-1.945,0.921,2.919				c0,2.919,1.8,12.275,2.764,3.892c-0.079-2.746,1.164-8.423,1.843-25.936c0-2.919,1.25-7.7,4.936-13.538				c1.843-2.919,13.136-30.098,13.136-39.827c0-3.892,14.844-39.134,14.844-48.863C86.609,118.001,91.359,105.743,87.75,94.917z				 M212.27,244.259c-1.764-1.75-5.014-9.061-5.936-11.007s-8.315-5.838-8.315-9.729c-2.255-21.044-8.857-49.14-9.778-52.059				c-0.922-3.892-4.607-11.676-5.529-15.567c-0.921-8.756-4.606-20.432-6.449-24.323c-1.843-6.811-1.866-10.702-1.866-13.621				c-0.229-3.394-0.608-6.364-1.508-8.942c-2.712-7.77-7.14-11.974-11.29-13.435c-1.994-0.67-3.841-0.987-5.571-1.227				c-0.298,0.751-0.671,1.629-0.999,2.498c0.086-0.187-0.565,2.076-0.15,7.18c0.543,6.646,2.213,17.229,1.113,22.466				c0,9.729,14.845,44.972,14.845,48.863c0,9.729,11.876,40.158,13.719,43.077c3.686,5.838,3.686,10.702,3.686,13.621				c1.278,17.513,0.588,19.057,1.509,22.603c1.765,7.983,2.765-0.973,2.765-3.892c0-4.864,0.922-2.919,0.922-2.919				c0.921,3.892,1.121,12.648,2.042,13.621c2.765,0.973,1.644-10.702,1.644-10.702c0-4.864,0.921-2.919,0.921-2.919				c0,2.919,2.243,12.648,2.243,12.648c3.686,4.864,2.364-2.919,2.364-5.838c-0.922-8.756,0-7.783,0-7.783l2.764,10.702				c1.844,4.864,2.164,0,2.164,0c0-3.892-2.164-15.566-2.164-15.566c1.129,1.191,2.521,3.425,3.543,3.851				C211.271,246.824,212.724,244.709,212.27,244.259z",
        "path://M154.877,104.026c-0.415-5.103,0.236-7.366,0.15-7.18c0.328-0.87,0.702-1.748,1-2.499				c-2.564-0.354-4.857-0.523-6.89-1.344c-1.339-0.541-2.565-1.072-3.689-1.589c-2.915,2.18-9.106,5.815-23.198,5.335				c-12.219-0.417-19.671-3.291-23.582-5.427c-1.649,0.771-3.513,1.578-5.637,2.408c-1.654,0.647-3.465,0.73-5.43,0.896				c0.078,0.133,0.13,0.233,0.149,0.29c3.609,10.826-1.141,23.084,0.26,31.575c0,0,2.371,17.832,4.741,28.982				c4.221-1.504,10.153-3.523,14.706-4.374c4.68-0.875,9.983-1.627,15.293-1.351c7.18,0.373,13.266,1.431,17.996,2.335				c3.427,0.655,7.604,1.335,10.58,1.815c1.916-11.175,4.664-27.408,4.664-27.408C157.09,121.256,155.42,110.673,154.877,104.026z",
        "path://M155.328,193.896c-0.922-6.811-6.666-24.579-4.866-34.979c0.254-1.468,0.549-3.182,0.864-5.017				c-2.976-0.48-7.153-1.16-10.58-1.815c-4.73-0.904-10.816-1.962-17.996-2.335c-5.31-0.276-10.613,0.476-15.293,1.351				c-10.072,1.882-14.755,4.496-14.724,4.394c-0.013,0.038,0.494,2.179,0.805,3.423c2.6,10.399-3.944,28.168-4.866,34.979				c-0.088,0.279-0.201,0.859-0.332,1.688c3.913,1.58,16.325,5.753,36.16,4.666c17.666-0.968,27.332-2.77,31.307-3.685				C155.611,195.203,155.447,194.274,155.328,193.896z",
        "path://M159.268,211.039c-0.441-4.467-1.216-8.439-1.735-10.794c0.19-0.086,0.356-0.169,0.468-0.245				c0.864-0.588,0-4,0-4s-9.166,2.917-33.5,4.25C100.167,201.583,87,195,87,195s-0.667,1.667-1,3				c-0.054,0.214,0.171,0.455,0.607,0.713c-0.681,3.063-2.089,9.854-2.922,17.071c-1.336,11.574-0.92,17.69-0.185,18.216				c2.333,1.667,36.167,4.75,37.166,2.75c1-2,1-2,1-2l2.043-0.167L124,235.75c0,0,0.666,3,12,2s24.804-2.678,24.75-5.75				C160.689,228.563,160.285,221.326,159.268,211.039z",
        "path://M154.25,321.191c-1.844-5.838-0.922-16.54,0-20.432c3.686-15.567,5.527-32.976,5.527-46.597				c0-6.154-0.268-13.261-0.674-20.391c-3.922,1.98-14.174,3.189-23.104,3.978c-8.324,0.734-10.892-0.688-11.67-1.496				c1.333,19.9,4.12,30.385,4.12,37.155c0,21.404,5.528,44.863,5.528,53.619c0,8.757-6.084,21.297-2.741,37.161				c3.686,8.757,2.741,49.075,2.741,50.048c-0.922,6.811-2.764,8.757-2.764,10.703c0,1.945,2.764,4.864,0.921,7.783				s0,7.783,1.843,9.729c1.843,1.945,5.528-1.946,13.82-0.974c8.293,0.974,5.529-3.892,5.529-3.892				c-3.686-0.973-5.529-5.837-5.529-5.837s0.922-6.811,0-8.757c-2.764-8.757,2.766-51.021,4.607-55.886				C157.936,354.46,156.092,328.002,154.25,321.191z M84.857,234.48c-0.384,6.892-0.635,13.732-0.635,19.683				c0,13.621,1.843,31.029,5.529,46.597c0.921,3.892,1.843,14.594,0,20.432c-1.843,6.811-3.686,33.269,1.843,45.917				c1.843,4.864,7.371,47.129,4.607,55.886c-0.921,1.946,0,8.757,0,8.757s-1.843,4.864-5.528,5.837c0,0-2.765,4.865,5.528,3.892				c8.292-0.973,11.978,2.919,13.821,0.974c1.843-1.946,3.686-6.811,1.843-9.729c-1.843-2.919,0.921-5.838,0.921-7.783				c0-1.946-1.843-3.893-2.765-10.703c0-0.973-0.944-41.291,2.742-50.048c1.843-17.364-2.742-28.404-2.742-37.161				c0-8.756,5.529-32.215,5.529-53.619c0-6.662,2.696-16.941,4.052-36.231C114.286,238.093,90.977,236.009,84.857,234.48z",
        "path://M90.652,31H90C90,31,90.179,31.368,90.652,31z M152,38c-0.584-3.417-1.834-9.667-3-11				c0.916,0.083,3-1,3-1s-1.084-1.333-3-2s-7.334-7.333-10-10s-8-4-8-4s0.311-0.771,1-1c0.802-0.268,2,0,2,0s-1.584-1-4-1				c-1.609,0-2.847,1.771-3.495,2.957C125.859,9.718,124.62,7.727,123,7c-2.416-1.083-3-1-3-1s1.75,2.083,2,3c0,0-1.5-1.167-5-1				c-1.497,0.071-2.445,0.265-3.04,0.462C113.164,8.727,113,9,113,9h1c0,0-4.333,0.417-8,2s-9,6-9,6h2c0,0-3.167,4.083-4,5				c-2.167,0.333-2,0-2,0s-0.417,0.417,1,1s1,1,1,1s-1.667,2.333-3,5c-0.6,1.2-1.037,1.758-1.348,2H92c0,0-0.004,1.607-1,5				c-0.714,2.434-0.287,8.385,0,9c0,0,0.75-4.083,1-3s-0.167,1.583,0,4s1,3,1,3l1-2c0,0,0.917,4.5,1,6s0,2,0,2l1-2v1l0.569-0.854				c0.529,0.498,1.135,0.37,1.635,0.552c-1.379,3.658,1.457,7.579,3.234,10.255c1.909,2.873,3.84,2.011,3.84,2.011l-0.079-0.541				c0.178,0.653,0.354,1.271,0.523,1.804c2.219,6.977,3.079,8.363,7.593,12.147c3.386,2.838,3.315,4.626,8.783,4.626				c5.669,0,5.154-1.94,8.896-4.974c3.35-2.716,5.586-4.172,7.426-10.634c0.188-0.662,0.404-1.426,0.631-2.23				c0.561,0.123,2.009,0.171,3.472-2.062c1.644-2.51,4.214-6.127,3.333-9.58c0.46-0.254,1.039-0.327,1.322-1.365				c0.053-0.192,0.109-0.4,0.164-0.602C148.352,51.021,150,47,150,47v1.754c0.115-0.262,0.385-0.763,1-1.754c1.5-2.417,0-7,0-7l1,2				C152,42,152.584,41.417,152,38z M122.072,9.264C122.137,9.5,122.109,9.402,122.072,9.264L122.072,9.264z",
        "path://M134.9,85.032c-0.596-0.533-0.982-4.968-1.021-8.421c-0.864,0.812-1.817,1.55-2.885,2.415				C127.253,82.06,127.768,84,122.099,84c-5.468,0-5.397-1.788-8.783-4.626c-1.129-0.946-2.029-1.742-2.779-2.514				c-0.249,3.256-0.752,7.572-1.437,8.172l-10.432,6.291c3.911,2.136,11.363,5.01,23.582,5.427				c14.092,0.48,20.283-3.155,23.198-5.335C137.705,87.858,134.9,85.032,134.9,85.032z",
        "path://M88.01,126.492c0,0,0.01,0.076,0.023,0.171C88.019,126.561,88.01,126.492,88.01,126.492z				 M87.75,94.917c-0.019-0.057-0.071-0.157-0.149-0.29c1.097-0.092-2.202-0.059-5.199,0.948				c-4.025,1.417-8.266,5.413-11.037,12.738c-1.035,2.736-1.51,5.936-1.761,9.639c0,2.919-0.023,6.811-1.866,13.621				c-1.843,3.892-5.529,15.567-6.45,24.323c-0.921,3.892-4.607,11.676-5.528,15.567c-0.921,2.919-7.19,29.682-10.778,52.059				c0,3.892-7.395,7.783-8.316,9.729s-2.921,7.507-5.186,10.257c-0.319,0.387-0.823,2.312,0.518,2.218				c1.122-0.079,3.91-1.945,5.589-3.718c0,0-2.364,11.675-2.364,15.566c0,0,0.521,4.864,2.364,0l2.764-10.702				c0,0,0.921-0.973,0,7.783c0,2.919-1.321,10.702,2.364,5.838c0,0,2.243-9.729,2.243-12.648c0,0,0.922-1.945,0.922,2.919				c0,0-0.922,11.675,1.843,10.702c0.921-0.973,0.921-9.729,1.843-13.621c0,0,0.921-1.945,0.921,2.919				c0,2.919,1.8,12.275,2.764,3.892c-0.079-2.746,1.164-8.423,1.843-25.936c0-2.919,1.25-7.7,4.936-13.538				c1.843-2.919,13.136-30.098,13.136-39.827c0-3.892,14.844-39.134,14.844-48.863C86.609,118.001,91.359,105.743,87.75,94.917z				 M212.27,244.259c-1.764-1.75-5.014-9.061-5.936-11.007s-8.315-5.838-8.315-9.729c-2.255-21.044-8.857-49.14-9.778-52.059				c-0.922-3.892-4.607-11.676-5.529-15.567c-0.921-8.756-4.606-20.432-6.449-24.323c-1.843-6.811-1.866-10.702-1.866-13.621				c-0.229-3.394-0.608-6.364-1.508-8.942c-2.712-7.77-7.14-11.974-11.29-13.435c-1.994-0.67-3.841-0.987-5.571-1.227				c-0.298,0.751-0.671,1.629-0.999,2.498c0.086-0.187-0.565,2.076-0.15,7.18c0.543,6.646,2.213,17.229,1.113,22.466				c0,9.729,14.845,44.972,14.845,48.863c0,9.729,11.876,40.158,13.719,43.077c3.686,5.838,3.686,10.702,3.686,13.621				c1.278,17.513,0.588,19.057,1.509,22.603c1.765,7.983,2.765-0.973,2.765-3.892c0-4.864,0.922-2.919,0.922-2.919				c0.921,3.892,1.121,12.648,2.042,13.621c2.765,0.973,1.644-10.702,1.644-10.702c0-4.864,0.921-2.919,0.921-2.919				c0,2.919,2.243,12.648,2.243,12.648c3.686,4.864,2.364-2.919,2.364-5.838c-0.922-8.756,0-7.783,0-7.783l2.764,10.702				c1.844,4.864,2.164,0,2.164,0c0-3.892-2.164-15.566-2.164-15.566c1.129,1.191,2.521,3.425,3.543,3.851				C211.271,246.824,212.724,244.709,212.27,244.259z",
        "path://M154.877,104.026c-0.415-5.103,0.236-7.366,0.15-7.18c0.328-0.87,0.702-1.748,1-2.499				c-2.564-0.354-4.857-0.523-6.89-1.344c-1.339-0.541-2.565-1.072-3.689-1.589c-2.915,2.18-9.106,5.815-23.198,5.335				c-12.219-0.417-19.671-3.291-23.582-5.427c-1.649,0.771-3.513,1.578-5.637,2.408c-1.654,0.647-3.465,0.73-5.43,0.896				c0.078,0.133,0.13,0.233,0.149,0.29c3.609,10.826-1.141,23.084,0.26,31.575c0,0,2.371,17.832,4.741,28.982				c4.221-1.504,10.153-3.523,14.706-4.374c4.68-0.875,9.983-1.627,15.293-1.351c7.18,0.373,13.266,1.431,17.996,2.335				c3.427,0.655,7.604,1.335,10.58,1.815c1.916-11.175,4.664-27.408,4.664-27.408C157.09,121.256,155.42,110.673,154.877,104.026z",
        "path://M155.328,193.896c-0.922-6.811-6.666-24.579-4.866-34.979c0.254-1.468,0.549-3.182,0.864-5.017				c-2.976-0.48-7.153-1.16-10.58-1.815c-4.73-0.904-10.816-1.962-17.996-2.335c-5.31-0.276-10.613,0.476-15.293,1.351				c-10.072,1.882-14.755,4.496-14.724,4.394c-0.013,0.038,0.494,2.179,0.805,3.423c2.6,10.399-3.944,28.168-4.866,34.979				c-0.088,0.279-0.201,0.859-0.332,1.688c3.913,1.58,16.325,5.753,36.16,4.666c17.666-0.968,27.332-2.77,31.307-3.685				C155.611,195.203,155.447,194.274,155.328,193.896z",
        "path://M159.268,211.039c-0.441-4.467-1.216-8.439-1.735-10.794c0.19-0.086,0.356-0.169,0.468-0.245				c0.864-0.588,0-4,0-4s-9.166,2.917-33.5,4.25C100.167,201.583,87,195,87,195s-0.667,1.667-1,3				c-0.054,0.214,0.171,0.455,0.607,0.713c-0.681,3.063-2.089,9.854-2.922,17.071c-1.336,11.574-0.92,17.69-0.185,18.216				c2.333,1.667,36.167,4.75,37.166,2.75c1-2,1-2,1-2l2.043-0.167L124,235.75c0,0,0.666,3,12,2s24.804-2.678,24.75-5.75				C160.689,228.563,160.285,221.326,159.268,211.039z",
        "path://M154.25,321.191c-1.844-5.838-0.922-16.54,0-20.432c3.686-15.567,5.527-32.976,5.527-46.597				c0-6.154-0.268-13.261-0.674-20.391c-3.922,1.98-14.174,3.189-23.104,3.978c-8.324,0.734-10.892-0.688-11.67-1.496				c1.333,19.9,4.12,30.385,4.12,37.155c0,21.404,5.528,44.863,5.528,53.619c0,8.757-6.084,21.297-2.741,37.161				c3.686,8.757,2.741,49.075,2.741,50.048c-0.922,6.811-2.764,8.757-2.764,10.703c0,1.945,2.764,4.864,0.921,7.783				s0,7.783,1.843,9.729c1.843,1.945,5.528-1.946,13.82-0.974c8.293,0.974,5.529-3.892,5.529-3.892				c-3.686-0.973-5.529-5.837-5.529-5.837s0.922-6.811,0-8.757c-2.764-8.757,2.766-51.021,4.607-55.886				C157.936,354.46,156.092,328.002,154.25,321.191z M84.857,234.48c-0.384,6.892-0.635,13.732-0.635,19.683				c0,13.621,1.843,31.029,5.529,46.597c0.921,3.892,1.843,14.594,0,20.432c-1.843,6.811-3.686,33.269,1.843,45.917				c1.843,4.864,7.371,47.129,4.607,55.886c-0.921,1.946,0,8.757,0,8.757s-1.843,4.864-5.528,5.837c0,0-2.765,4.865,5.528,3.892				c8.292-0.973,11.978,2.919,13.821,0.974c1.843-1.946,3.686-6.811,1.843-9.729c-1.843-2.919,0.921-5.838,0.921-7.783				c0-1.946-1.843-3.893-2.765-10.703c0-0.973-0.944-41.291,2.742-50.048c1.843-17.364-2.742-28.404-2.742-37.161				c0-8.756,5.529-32.215,5.529-53.619c0-6.662,2.696-16.941,4.052-36.231C114.286,238.093,90.977,236.009,84.857,234.48z",
        "path://M90.652,31H90C90,31,90.179,31.368,90.652,31z M152,38c-0.584-3.417-1.834-9.667-3-11				c0.916,0.083,3-1,3-1s-1.084-1.333-3-2s-7.334-7.333-10-10s-8-4-8-4s0.311-0.771,1-1c0.802-0.268,2,0,2,0s-1.584-1-4-1				c-1.609,0-2.847,1.771-3.495,2.957C125.859,9.718,124.62,7.727,123,7c-2.416-1.083-3-1-3-1s1.75,2.083,2,3c0,0-1.5-1.167-5-1				c-1.497,0.071-2.445,0.265-3.04,0.462C113.164,8.727,113,9,113,9h1c0,0-4.333,0.417-8,2s-9,6-9,6h2c0,0-3.167,4.083-4,5				c-2.167,0.333-2,0-2,0s-0.417,0.417,1,1s1,1,1,1s-1.667,2.333-3,5c-0.6,1.2-1.037,1.758-1.348,2H92c0,0-0.004,1.607-1,5				c-0.714,2.434-0.287,8.385,0,9c0,0,0.75-4.083,1-3s-0.167,1.583,0,4s1,3,1,3l1-2c0,0,0.917,4.5,1,6s0,2,0,2l1-2v1l0.569-0.854				c0.529,0.498,1.135,0.37,1.635,0.552c-1.379,3.658,1.457,7.579,3.234,10.255c1.909,2.873,3.84,2.011,3.84,2.011l-0.079-0.541				c0.178,0.653,0.354,1.271,0.523,1.804c2.219,6.977,3.079,8.363,7.593,12.147c3.386,2.838,3.315,4.626,8.783,4.626				c5.669,0,5.154-1.94,8.896-4.974c3.35-2.716,5.586-4.172,7.426-10.634c0.188-0.662,0.404-1.426,0.631-2.23				c0.561,0.123,2.009,0.171,3.472-2.062c1.644-2.51,4.214-6.127,3.333-9.58c0.46-0.254,1.039-0.327,1.322-1.365				c0.053-0.192,0.109-0.4,0.164-0.602C148.352,51.021,150,47,150,47v1.754c0.115-0.262,0.385-0.763,1-1.754c1.5-2.417,0-7,0-7l1,2				C152,42,152.584,41.417,152,38z M122.072,9.264C122.137,9.5,122.109,9.402,122.072,9.264L122.072,9.264z",
        "path://M134.9,85.032c-0.596-0.533-0.982-4.968-1.021-8.421c-0.864,0.812-1.817,1.55-2.885,2.415				C127.253,82.06,127.768,84,122.099,84c-5.468,0-5.397-1.788-8.783-4.626c-1.129-0.946-2.029-1.742-2.779-2.514				c-0.249,3.256-0.752,7.572-1.437,8.172l-10.432,6.291c3.911,2.136,11.363,5.01,23.582,5.427				c14.092,0.48,20.283-3.155,23.198-5.335C137.705,87.858,134.9,85.032,134.9,85.032z",
        "path://M88.01,126.492c0,0,0.01,0.076,0.023,0.171C88.019,126.561,88.01,126.492,88.01,126.492z				 M87.75,94.917c-0.019-0.057-0.071-0.157-0.149-0.29c1.097-0.092-2.202-0.059-5.199,0.948				c-4.025,1.417-8.266,5.413-11.037,12.738c-1.035,2.736-1.51,5.936-1.761,9.639c0,2.919-0.023,6.811-1.866,13.621				c-1.843,3.892-5.529,15.567-6.45,24.323c-0.921,3.892-4.607,11.676-5.528,15.567c-0.921,2.919-7.19,29.682-10.778,52.059				c0,3.892-7.395,7.783-8.316,9.729s-2.921,7.507-5.186,10.257c-0.319,0.387-0.823,2.312,0.518,2.218				c1.122-0.079,3.91-1.945,5.589-3.718c0,0-2.364,11.675-2.364,15.566c0,0,0.521,4.864,2.364,0l2.764-10.702				c0,0,0.921-0.973,0,7.783c0,2.919-1.321,10.702,2.364,5.838c0,0,2.243-9.729,2.243-12.648c0,0,0.922-1.945,0.922,2.919				c0,0-0.922,11.675,1.843,10.702c0.921-0.973,0.921-9.729,1.843-13.621c0,0,0.921-1.945,0.921,2.919				c0,2.919,1.8,12.275,2.764,3.892c-0.079-2.746,1.164-8.423,1.843-25.936c0-2.919,1.25-7.7,4.936-13.538				c1.843-2.919,13.136-30.098,13.136-39.827c0-3.892,14.844-39.134,14.844-48.863C86.609,118.001,91.359,105.743,87.75,94.917z				 M212.27,244.259c-1.764-1.75-5.014-9.061-5.936-11.007s-8.315-5.838-8.315-9.729c-2.255-21.044-8.857-49.14-9.778-52.059				c-0.922-3.892-4.607-11.676-5.529-15.567c-0.921-8.756-4.606-20.432-6.449-24.323c-1.843-6.811-1.866-10.702-1.866-13.621				c-0.229-3.394-0.608-6.364-1.508-8.942c-2.712-7.77-7.14-11.974-11.29-13.435c-1.994-0.67-3.841-0.987-5.571-1.227				c-0.298,0.751-0.671,1.629-0.999,2.498c0.086-0.187-0.565,2.076-0.15,7.18c0.543,6.646,2.213,17.229,1.113,22.466				c0,9.729,14.845,44.972,14.845,48.863c0,9.729,11.876,40.158,13.719,43.077c3.686,5.838,3.686,10.702,3.686,13.621				c1.278,17.513,0.588,19.057,1.509,22.603c1.765,7.983,2.765-0.973,2.765-3.892c0-4.864,0.922-2.919,0.922-2.919				c0.921,3.892,1.121,12.648,2.042,13.621c2.765,0.973,1.644-10.702,1.644-10.702c0-4.864,0.921-2.919,0.921-2.919				c0,2.919,2.243,12.648,2.243,12.648c3.686,4.864,2.364-2.919,2.364-5.838c-0.922-8.756,0-7.783,0-7.783l2.764,10.702				c1.844,4.864,2.164,0,2.164,0c0-3.892-2.164-15.566-2.164-15.566c1.129,1.191,2.521,3.425,3.543,3.851				C211.271,246.824,212.724,244.709,212.27,244.259z",
        "path://M154.877,104.026c-0.415-5.103,0.236-7.366,0.15-7.18c0.328-0.87,0.702-1.748,1-2.499				c-2.564-0.354-4.857-0.523-6.89-1.344c-1.339-0.541-2.565-1.072-3.689-1.589c-2.915,2.18-9.106,5.815-23.198,5.335				c-12.219-0.417-19.671-3.291-23.582-5.427c-1.649,0.771-3.513,1.578-5.637,2.408c-1.654,0.647-3.465,0.73-5.43,0.896				c0.078,0.133,0.13,0.233,0.149,0.29c3.609,10.826-1.141,23.084,0.26,31.575c0,0,2.371,17.832,4.741,28.982				c4.221-1.504,10.153-3.523,14.706-4.374c4.68-0.875,9.983-1.627,15.293-1.351c7.18,0.373,13.266,1.431,17.996,2.335				c3.427,0.655,7.604,1.335,10.58,1.815c1.916-11.175,4.664-27.408,4.664-27.408C157.09,121.256,155.42,110.673,154.877,104.026z",
        "path://M155.328,193.896c-0.922-6.811-6.666-24.579-4.866-34.979c0.254-1.468,0.549-3.182,0.864-5.017				c-2.976-0.48-7.153-1.16-10.58-1.815c-4.73-0.904-10.816-1.962-17.996-2.335c-5.31-0.276-10.613,0.476-15.293,1.351				c-10.072,1.882-14.755,4.496-14.724,4.394c-0.013,0.038,0.494,2.179,0.805,3.423c2.6,10.399-3.944,28.168-4.866,34.979				c-0.088,0.279-0.201,0.859-0.332,1.688c3.913,1.58,16.325,5.753,36.16,4.666c17.666-0.968,27.332-2.77,31.307-3.685				C155.611,195.203,155.447,194.274,155.328,193.896z",
        "path://M159.268,211.039c-0.441-4.467-1.216-8.439-1.735-10.794c0.19-0.086,0.356-0.169,0.468-0.245				c0.864-0.588,0-4,0-4s-9.166,2.917-33.5,4.25C100.167,201.583,87,195,87,195s-0.667,1.667-1,3				c-0.054,0.214,0.171,0.455,0.607,0.713c-0.681,3.063-2.089,9.854-2.922,17.071c-1.336,11.574-0.92,17.69-0.185,18.216				c2.333,1.667,36.167,4.75,37.166,2.75c1-2,1-2,1-2l2.043-0.167L124,235.75c0,0,0.666,3,12,2s24.804-2.678,24.75-5.75				C160.689,228.563,160.285,221.326,159.268,211.039z",
        "path://M154.25,321.191c-1.844-5.838-0.922-16.54,0-20.432c3.686-15.567,5.527-32.976,5.527-46.597				c0-6.154-0.268-13.261-0.674-20.391c-3.922,1.98-14.174,3.189-23.104,3.978c-8.324,0.734-10.892-0.688-11.67-1.496				c1.333,19.9,4.12,30.385,4.12,37.155c0,21.404,5.528,44.863,5.528,53.619c0,8.757-6.084,21.297-2.741,37.161				c3.686,8.757,2.741,49.075,2.741,50.048c-0.922,6.811-2.764,8.757-2.764,10.703c0,1.945,2.764,4.864,0.921,7.783				s0,7.783,1.843,9.729c1.843,1.945,5.528-1.946,13.82-0.974c8.293,0.974,5.529-3.892,5.529-3.892				c-3.686-0.973-5.529-5.837-5.529-5.837s0.922-6.811,0-8.757c-2.764-8.757,2.766-51.021,4.607-55.886				C157.936,354.46,156.092,328.002,154.25,321.191z M84.857,234.48c-0.384,6.892-0.635,13.732-0.635,19.683				c0,13.621,1.843,31.029,5.529,46.597c0.921,3.892,1.843,14.594,0,20.432c-1.843,6.811-3.686,33.269,1.843,45.917				c1.843,4.864,7.371,47.129,4.607,55.886c-0.921,1.946,0,8.757,0,8.757s-1.843,4.864-5.528,5.837c0,0-2.765,4.865,5.528,3.892				c8.292-0.973,11.978,2.919,13.821,0.974c1.843-1.946,3.686-6.811,1.843-9.729c-1.843-2.919,0.921-5.838,0.921-7.783				c0-1.946-1.843-3.893-2.765-10.703c0-0.973-0.944-41.291,2.742-50.048c1.843-17.364-2.742-28.404-2.742-37.161				c0-8.756,5.529-32.215,5.529-53.619c0-6.662,2.696-16.941,4.052-36.231C114.286,238.093,90.977,236.009,84.857,234.48z",
        "path://M90.652,31H90C90,31,90.179,31.368,90.652,31z M152,38c-0.584-3.417-1.834-9.667-3-11				c0.916,0.083,3-1,3-1s-1.084-1.333-3-2s-7.334-7.333-10-10s-8-4-8-4s0.311-0.771,1-1c0.802-0.268,2,0,2,0s-1.584-1-4-1				c-1.609,0-2.847,1.771-3.495,2.957C125.859,9.718,124.62,7.727,123,7c-2.416-1.083-3-1-3-1s1.75,2.083,2,3c0,0-1.5-1.167-5-1				c-1.497,0.071-2.445,0.265-3.04,0.462C113.164,8.727,113,9,113,9h1c0,0-4.333,0.417-8,2s-9,6-9,6h2c0,0-3.167,4.083-4,5				c-2.167,0.333-2,0-2,0s-0.417,0.417,1,1s1,1,1,1s-1.667,2.333-3,5c-0.6,1.2-1.037,1.758-1.348,2H92c0,0-0.004,1.607-1,5				c-0.714,2.434-0.287,8.385,0,9c0,0,0.75-4.083,1-3s-0.167,1.583,0,4s1,3,1,3l1-2c0,0,0.917,4.5,1,6s0,2,0,2l1-2v1l0.569-0.854				c0.529,0.498,1.135,0.37,1.635,0.552c-1.379,3.658,1.457,7.579,3.234,10.255c1.909,2.873,3.84,2.011,3.84,2.011l-0.079-0.541				c0.178,0.653,0.354,1.271,0.523,1.804c2.219,6.977,3.079,8.363,7.593,12.147c3.386,2.838,3.315,4.626,8.783,4.626				c5.669,0,5.154-1.94,8.896-4.974c3.35-2.716,5.586-4.172,7.426-10.634c0.188-0.662,0.404-1.426,0.631-2.23				c0.561,0.123,2.009,0.171,3.472-2.062c1.644-2.51,4.214-6.127,3.333-9.58c0.46-0.254,1.039-0.327,1.322-1.365				c0.053-0.192,0.109-0.4,0.164-0.602C148.352,51.021,150,47,150,47v1.754c0.115-0.262,0.385-0.763,1-1.754c1.5-2.417,0-7,0-7l1,2				C152,42,152.584,41.417,152,38z M122.072,9.264C122.137,9.5,122.109,9.402,122.072,9.264L122.072,9.264z",
        "path://M134.9,85.032c-0.596-0.533-0.982-4.968-1.021-8.421c-0.864,0.812-1.817,1.55-2.885,2.415				C127.253,82.06,127.768,84,122.099,84c-5.468,0-5.397-1.788-8.783-4.626c-1.129-0.946-2.029-1.742-2.779-2.514				c-0.249,3.256-0.752,7.572-1.437,8.172l-10.432,6.291c3.911,2.136,11.363,5.01,23.582,5.427				c14.092,0.48,20.283-3.155,23.198-5.335C137.705,87.858,134.9,85.032,134.9,85.032z",
        "path://M88.01,126.492c0,0,0.01,0.076,0.023,0.171C88.019,126.561,88.01,126.492,88.01,126.492z				 M87.75,94.917c-0.019-0.057-0.071-0.157-0.149-0.29c1.097-0.092-2.202-0.059-5.199,0.948				c-4.025,1.417-8.266,5.413-11.037,12.738c-1.035,2.736-1.51,5.936-1.761,9.639c0,2.919-0.023,6.811-1.866,13.621				c-1.843,3.892-5.529,15.567-6.45,24.323c-0.921,3.892-4.607,11.676-5.528,15.567c-0.921,2.919-7.19,29.682-10.778,52.059				c0,3.892-7.395,7.783-8.316,9.729s-2.921,7.507-5.186,10.257c-0.319,0.387-0.823,2.312,0.518,2.218				c1.122-0.079,3.91-1.945,5.589-3.718c0,0-2.364,11.675-2.364,15.566c0,0,0.521,4.864,2.364,0l2.764-10.702				c0,0,0.921-0.973,0,7.783c0,2.919-1.321,10.702,2.364,5.838c0,0,2.243-9.729,2.243-12.648c0,0,0.922-1.945,0.922,2.919				c0,0-0.922,11.675,1.843,10.702c0.921-0.973,0.921-9.729,1.843-13.621c0,0,0.921-1.945,0.921,2.919				c0,2.919,1.8,12.275,2.764,3.892c-0.079-2.746,1.164-8.423,1.843-25.936c0-2.919,1.25-7.7,4.936-13.538				c1.843-2.919,13.136-30.098,13.136-39.827c0-3.892,14.844-39.134,14.844-48.863C86.609,118.001,91.359,105.743,87.75,94.917z				 M212.27,244.259c-1.764-1.75-5.014-9.061-5.936-11.007s-8.315-5.838-8.315-9.729c-2.255-21.044-8.857-49.14-9.778-52.059				c-0.922-3.892-4.607-11.676-5.529-15.567c-0.921-8.756-4.606-20.432-6.449-24.323c-1.843-6.811-1.866-10.702-1.866-13.621				c-0.229-3.394-0.608-6.364-1.508-8.942c-2.712-7.77-7.14-11.974-11.29-13.435c-1.994-0.67-3.841-0.987-5.571-1.227				c-0.298,0.751-0.671,1.629-0.999,2.498c0.086-0.187-0.565,2.076-0.15,7.18c0.543,6.646,2.213,17.229,1.113,22.466				c0,9.729,14.845,44.972,14.845,48.863c0,9.729,11.876,40.158,13.719,43.077c3.686,5.838,3.686,10.702,3.686,13.621				c1.278,17.513,0.588,19.057,1.509,22.603c1.765,7.983,2.765-0.973,2.765-3.892c0-4.864,0.922-2.919,0.922-2.919				c0.921,3.892,1.121,12.648,2.042,13.621c2.765,0.973,1.644-10.702,1.644-10.702c0-4.864,0.921-2.919,0.921-2.919				c0,2.919,2.243,12.648,2.243,12.648c3.686,4.864,2.364-2.919,2.364-5.838c-0.922-8.756,0-7.783,0-7.783l2.764,10.702				c1.844,4.864,2.164,0,2.164,0c0-3.892-2.164-15.566-2.164-15.566c1.129,1.191,2.521,3.425,3.543,3.851				C211.271,246.824,212.724,244.709,212.27,244.259z",
        "path://M154.877,104.026c-0.415-5.103,0.236-7.366,0.15-7.18c0.328-0.87,0.702-1.748,1-2.499				c-2.564-0.354-4.857-0.523-6.89-1.344c-1.339-0.541-2.565-1.072-3.689-1.589c-2.915,2.18-9.106,5.815-23.198,5.335				c-12.219-0.417-19.671-3.291-23.582-5.427c-1.649,0.771-3.513,1.578-5.637,2.408c-1.654,0.647-3.465,0.73-5.43,0.896				c0.078,0.133,0.13,0.233,0.149,0.29c3.609,10.826-1.141,23.084,0.26,31.575c0,0,2.371,17.832,4.741,28.982				c4.221-1.504,10.153-3.523,14.706-4.374c4.68-0.875,9.983-1.627,15.293-1.351c7.18,0.373,13.266,1.431,17.996,2.335				c3.427,0.655,7.604,1.335,10.58,1.815c1.916-11.175,4.664-27.408,4.664-27.408C157.09,121.256,155.42,110.673,154.877,104.026z",
        "path://M155.328,193.896c-0.922-6.811-6.666-24.579-4.866-34.979c0.254-1.468,0.549-3.182,0.864-5.017				c-2.976-0.48-7.153-1.16-10.58-1.815c-4.73-0.904-10.816-1.962-17.996-2.335c-5.31-0.276-10.613,0.476-15.293,1.351				c-10.072,1.882-14.755,4.496-14.724,4.394c-0.013,0.038,0.494,2.179,0.805,3.423c2.6,10.399-3.944,28.168-4.866,34.979				c-0.088,0.279-0.201,0.859-0.332,1.688c3.913,1.58,16.325,5.753,36.16,4.666c17.666-0.968,27.332-2.77,31.307-3.685				C155.611,195.203,155.447,194.274,155.328,193.896z",
        "path://M159.268,211.039c-0.441-4.467-1.216-8.439-1.735-10.794c0.19-0.086,0.356-0.169,0.468-0.245				c0.864-0.588,0-4,0-4s-9.166,2.917-33.5,4.25C100.167,201.583,87,195,87,195s-0.667,1.667-1,3				c-0.054,0.214,0.171,0.455,0.607,0.713c-0.681,3.063-2.089,9.854-2.922,17.071c-1.336,11.574-0.92,17.69-0.185,18.216				c2.333,1.667,36.167,4.75,37.166,2.75c1-2,1-2,1-2l2.043-0.167L124,235.75c0,0,0.666,3,12,2s24.804-2.678,24.75-5.75				C160.689,228.563,160.285,221.326,159.268,211.039z",
        "path://M154.25,321.191c-1.844-5.838-0.922-16.54,0-20.432c3.686-15.567,5.527-32.976,5.527-46.597				c0-6.154-0.268-13.261-0.674-20.391c-3.922,1.98-14.174,3.189-23.104,3.978c-8.324,0.734-10.892-0.688-11.67-1.496				c1.333,19.9,4.12,30.385,4.12,37.155c0,21.404,5.528,44.863,5.528,53.619c0,8.757-6.084,21.297-2.741,37.161				c3.686,8.757,2.741,49.075,2.741,50.048c-0.922,6.811-2.764,8.757-2.764,10.703c0,1.945,2.764,4.864,0.921,7.783				s0,7.783,1.843,9.729c1.843,1.945,5.528-1.946,13.82-0.974c8.293,0.974,5.529-3.892,5.529-3.892				c-3.686-0.973-5.529-5.837-5.529-5.837s0.922-6.811,0-8.757c-2.764-8.757,2.766-51.021,4.607-55.886				C157.936,354.46,156.092,328.002,154.25,321.191z M84.857,234.48c-0.384,6.892-0.635,13.732-0.635,19.683				c0,13.621,1.843,31.029,5.529,46.597c0.921,3.892,1.843,14.594,0,20.432c-1.843,6.811-3.686,33.269,1.843,45.917				c1.843,4.864,7.371,47.129,4.607,55.886c-0.921,1.946,0,8.757,0,8.757s-1.843,4.864-5.528,5.837c0,0-2.765,4.865,5.528,3.892				c8.292-0.973,11.978,2.919,13.821,0.974c1.843-1.946,3.686-6.811,1.843-9.729c-1.843-2.919,0.921-5.838,0.921-7.783				c0-1.946-1.843-3.893-2.765-10.703c0-0.973-0.944-41.291,2.742-50.048c1.843-17.364-2.742-28.404-2.742-37.161				c0-8.756,5.529-32.215,5.529-53.619c0-6.662,2.696-16.941,4.052-36.231C114.286,238.093,90.977,236.009,84.857,234.48z",
        "path://M90.652,31H90C90,31,90.179,31.368,90.652,31z M152,38c-0.584-3.417-1.834-9.667-3-11				c0.916,0.083,3-1,3-1s-1.084-1.333-3-2s-7.334-7.333-10-10s-8-4-8-4s0.311-0.771,1-1c0.802-0.268,2,0,2,0s-1.584-1-4-1				c-1.609,0-2.847,1.771-3.495,2.957C125.859,9.718,124.62,7.727,123,7c-2.416-1.083-3-1-3-1s1.75,2.083,2,3c0,0-1.5-1.167-5-1				c-1.497,0.071-2.445,0.265-3.04,0.462C113.164,8.727,113,9,113,9h1c0,0-4.333,0.417-8,2s-9,6-9,6h2c0,0-3.167,4.083-4,5				c-2.167,0.333-2,0-2,0s-0.417,0.417,1,1s1,1,1,1s-1.667,2.333-3,5c-0.6,1.2-1.037,1.758-1.348,2H92c0,0-0.004,1.607-1,5				c-0.714,2.434-0.287,8.385,0,9c0,0,0.75-4.083,1-3s-0.167,1.583,0,4s1,3,1,3l1-2c0,0,0.917,4.5,1,6s0,2,0,2l1-2v1l0.569-0.854				c0.529,0.498,1.135,0.37,1.635,0.552c-1.379,3.658,1.457,7.579,3.234,10.255c1.909,2.873,3.84,2.011,3.84,2.011l-0.079-0.541				c0.178,0.653,0.354,1.271,0.523,1.804c2.219,6.977,3.079,8.363,7.593,12.147c3.386,2.838,3.315,4.626,8.783,4.626				c5.669,0,5.154-1.94,8.896-4.974c3.35-2.716,5.586-4.172,7.426-10.634c0.188-0.662,0.404-1.426,0.631-2.23				c0.561,0.123,2.009,0.171,3.472-2.062c1.644-2.51,4.214-6.127,3.333-9.58c0.46-0.254,1.039-0.327,1.322-1.365				c0.053-0.192,0.109-0.4,0.164-0.602C148.352,51.021,150,47,150,47v1.754c0.115-0.262,0.385-0.763,1-1.754c1.5-2.417,0-7,0-7l1,2				C152,42,152.584,41.417,152,38z M122.072,9.264C122.137,9.5,122.109,9.402,122.072,9.264L122.072,9.264z",
        "path://M134.9,85.032c-0.596-0.533-0.982-4.968-1.021-8.421c-0.864,0.812-1.817,1.55-2.885,2.415				C127.253,82.06,127.768,84,122.099,84c-5.468,0-5.397-1.788-8.783-4.626c-1.129-0.946-2.029-1.742-2.779-2.514				c-0.249,3.256-0.752,7.572-1.437,8.172l-10.432,6.291c3.911,2.136,11.363,5.01,23.582,5.427				c14.092,0.48,20.283-3.155,23.198-5.335C137.705,87.858,134.9,85.032,134.9,85.032z",
        "path://M88.01,126.492c0,0,0.01,0.076,0.023,0.171C88.019,126.561,88.01,126.492,88.01,126.492z				 M87.75,94.917c-0.019-0.057-0.071-0.157-0.149-0.29c1.097-0.092-2.202-0.059-5.199,0.948				c-4.025,1.417-8.266,5.413-11.037,12.738c-1.035,2.736-1.51,5.936-1.761,9.639c0,2.919-0.023,6.811-1.866,13.621				c-1.843,3.892-5.529,15.567-6.45,24.323c-0.921,3.892-4.607,11.676-5.528,15.567c-0.921,2.919-7.19,29.682-10.778,52.059				c0,3.892-7.395,7.783-8.316,9.729s-2.921,7.507-5.186,10.257c-0.319,0.387-0.823,2.312,0.518,2.218				c1.122-0.079,3.91-1.945,5.589-3.718c0,0-2.364,11.675-2.364,15.566c0,0,0.521,4.864,2.364,0l2.764-10.702				c0,0,0.921-0.973,0,7.783c0,2.919-1.321,10.702,2.364,5.838c0,0,2.243-9.729,2.243-12.648c0,0,0.922-1.945,0.922,2.919				c0,0-0.922,11.675,1.843,10.702c0.921-0.973,0.921-9.729,1.843-13.621c0,0,0.921-1.945,0.921,2.919				c0,2.919,1.8,12.275,2.764,3.892c-0.079-2.746,1.164-8.423,1.843-25.936c0-2.919,1.25-7.7,4.936-13.538				c1.843-2.919,13.136-30.098,13.136-39.827c0-3.892,14.844-39.134,14.844-48.863C86.609,118.001,91.359,105.743,87.75,94.917z				 M212.27,244.259c-1.764-1.75-5.014-9.061-5.936-11.007s-8.315-5.838-8.315-9.729c-2.255-21.044-8.857-49.14-9.778-52.059				c-0.922-3.892-4.607-11.676-5.529-15.567c-0.921-8.756-4.606-20.432-6.449-24.323c-1.843-6.811-1.866-10.702-1.866-13.621				c-0.229-3.394-0.608-6.364-1.508-8.942c-2.712-7.77-7.14-11.974-11.29-13.435c-1.994-0.67-3.841-0.987-5.571-1.227				c-0.298,0.751-0.671,1.629-0.999,2.498c0.086-0.187-0.565,2.076-0.15,7.18c0.543,6.646,2.213,17.229,1.113,22.466				c0,9.729,14.845,44.972,14.845,48.863c0,9.729,11.876,40.158,13.719,43.077c3.686,5.838,3.686,10.702,3.686,13.621				c1.278,17.513,0.588,19.057,1.509,22.603c1.765,7.983,2.765-0.973,2.765-3.892c0-4.864,0.922-2.919,0.922-2.919				c0.921,3.892,1.121,12.648,2.042,13.621c2.765,0.973,1.644-10.702,1.644-10.702c0-4.864,0.921-2.919,0.921-2.919				c0,2.919,2.243,12.648,2.243,12.648c3.686,4.864,2.364-2.919,2.364-5.838c-0.922-8.756,0-7.783,0-7.783l2.764,10.702				c1.844,4.864,2.164,0,2.164,0c0-3.892-2.164-15.566-2.164-15.566c1.129,1.191,2.521,3.425,3.543,3.851				C211.271,246.824,212.724,244.709,212.27,244.259z",
        "path://M154.877,104.026c-0.415-5.103,0.236-7.366,0.15-7.18c0.328-0.87,0.702-1.748,1-2.499				c-2.564-0.354-4.857-0.523-6.89-1.344c-1.339-0.541-2.565-1.072-3.689-1.589c-2.915,2.18-9.106,5.815-23.198,5.335				c-12.219-0.417-19.671-3.291-23.582-5.427c-1.649,0.771-3.513,1.578-5.637,2.408c-1.654,0.647-3.465,0.73-5.43,0.896				c0.078,0.133,0.13,0.233,0.149,0.29c3.609,10.826-1.141,23.084,0.26,31.575c0,0,2.371,17.832,4.741,28.982				c4.221-1.504,10.153-3.523,14.706-4.374c4.68-0.875,9.983-1.627,15.293-1.351c7.18,0.373,13.266,1.431,17.996,2.335				c3.427,0.655,7.604,1.335,10.58,1.815c1.916-11.175,4.664-27.408,4.664-27.408C157.09,121.256,155.42,110.673,154.877,104.026z",
        "path://M155.328,193.896c-0.922-6.811-6.666-24.579-4.866-34.979c0.254-1.468,0.549-3.182,0.864-5.017				c-2.976-0.48-7.153-1.16-10.58-1.815c-4.73-0.904-10.816-1.962-17.996-2.335c-5.31-0.276-10.613,0.476-15.293,1.351				c-10.072,1.882-14.755,4.496-14.724,4.394c-0.013,0.038,0.494,2.179,0.805,3.423c2.6,10.399-3.944,28.168-4.866,34.979				c-0.088,0.279-0.201,0.859-0.332,1.688c3.913,1.58,16.325,5.753,36.16,4.666c17.666-0.968,27.332-2.77,31.307-3.685				C155.611,195.203,155.447,194.274,155.328,193.896z",
        "path://M159.268,211.039c-0.441-4.467-1.216-8.439-1.735-10.794c0.19-0.086,0.356-0.169,0.468-0.245				c0.864-0.588,0-4,0-4s-9.166,2.917-33.5,4.25C100.167,201.583,87,195,87,195s-0.667,1.667-1,3				c-0.054,0.214,0.171,0.455,0.607,0.713c-0.681,3.063-2.089,9.854-2.922,17.071c-1.336,11.574-0.92,17.69-0.185,18.216				c2.333,1.667,36.167,4.75,37.166,2.75c1-2,1-2,1-2l2.043-0.167L124,235.75c0,0,0.666,3,12,2s24.804-2.678,24.75-5.75				C160.689,228.563,160.285,221.326,159.268,211.039z",
        "path://M154.25,321.191c-1.844-5.838-0.922-16.54,0-20.432c3.686-15.567,5.527-32.976,5.527-46.597				c0-6.154-0.268-13.261-0.674-20.391c-3.922,1.98-14.174,3.189-23.104,3.978c-8.324,0.734-10.892-0.688-11.67-1.496				c1.333,19.9,4.12,30.385,4.12,37.155c0,21.404,5.528,44.863,5.528,53.619c0,8.757-6.084,21.297-2.741,37.161				c3.686,8.757,2.741,49.075,2.741,50.048c-0.922,6.811-2.764,8.757-2.764,10.703c0,1.945,2.764,4.864,0.921,7.783				s0,7.783,1.843,9.729c1.843,1.945,5.528-1.946,13.82-0.974c8.293,0.974,5.529-3.892,5.529-3.892				c-3.686-0.973-5.529-5.837-5.529-5.837s0.922-6.811,0-8.757c-2.764-8.757,2.766-51.021,4.607-55.886				C157.936,354.46,156.092,328.002,154.25,321.191z M84.857,234.48c-0.384,6.892-0.635,13.732-0.635,19.683				c0,13.621,1.843,31.029,5.529,46.597c0.921,3.892,1.843,14.594,0,20.432c-1.843,6.811-3.686,33.269,1.843,45.917				c1.843,4.864,7.371,47.129,4.607,55.886c-0.921,1.946,0,8.757,0,8.757s-1.843,4.864-5.528,5.837c0,0-2.765,4.865,5.528,3.892				c8.292-0.973,11.978,2.919,13.821,0.974c1.843-1.946,3.686-6.811,1.843-9.729c-1.843-2.919,0.921-5.838,0.921-7.783				c0-1.946-1.843-3.893-2.765-10.703c0-0.973-0.944-41.291,2.742-50.048c1.843-17.364-2.742-28.404-2.742-37.161				c0-8.756,5.529-32.215,5.529-53.619c0-6.662,2.696-16.941,4.052-36.231C114.286,238.093,90.977,236.009,84.857,234.48z",
        "path://M90.652,31H90C90,31,90.179,31.368,90.652,31z M152,38c-0.584-3.417-1.834-9.667-3-11				c0.916,0.083,3-1,3-1s-1.084-1.333-3-2s-7.334-7.333-10-10s-8-4-8-4s0.311-0.771,1-1c0.802-0.268,2,0,2,0s-1.584-1-4-1				c-1.609,0-2.847,1.771-3.495,2.957C125.859,9.718,124.62,7.727,123,7c-2.416-1.083-3-1-3-1s1.75,2.083,2,3c0,0-1.5-1.167-5-1				c-1.497,0.071-2.445,0.265-3.04,0.462C113.164,8.727,113,9,113,9h1c0,0-4.333,0.417-8,2s-9,6-9,6h2c0,0-3.167,4.083-4,5				c-2.167,0.333-2,0-2,0s-0.417,0.417,1,1s1,1,1,1s-1.667,2.333-3,5c-0.6,1.2-1.037,1.758-1.348,2H92c0,0-0.004,1.607-1,5				c-0.714,2.434-0.287,8.385,0,9c0,0,0.75-4.083,1-3s-0.167,1.583,0,4s1,3,1,3l1-2c0,0,0.917,4.5,1,6s0,2,0,2l1-2v1l0.569-0.854				c0.529,0.498,1.135,0.37,1.635,0.552c-1.379,3.658,1.457,7.579,3.234,10.255c1.909,2.873,3.84,2.011,3.84,2.011l-0.079-0.541				c0.178,0.653,0.354,1.271,0.523,1.804c2.219,6.977,3.079,8.363,7.593,12.147c3.386,2.838,3.315,4.626,8.783,4.626				c5.669,0,5.154-1.94,8.896-4.974c3.35-2.716,5.586-4.172,7.426-10.634c0.188-0.662,0.404-1.426,0.631-2.23				c0.561,0.123,2.009,0.171,3.472-2.062c1.644-2.51,4.214-6.127,3.333-9.58c0.46-0.254,1.039-0.327,1.322-1.365				c0.053-0.192,0.109-0.4,0.164-0.602C148.352,51.021,150,47,150,47v1.754c0.115-0.262,0.385-0.763,1-1.754c1.5-2.417,0-7,0-7l1,2				C152,42,152.584,41.417,152,38z M122.072,9.264C122.137,9.5,122.109,9.402,122.072,9.264L122.072,9.264z",
        "path://M134.9,85.032c-0.596-0.533-0.982-4.968-1.021-8.421c-0.864,0.812-1.817,1.55-2.885,2.415				C127.253,82.06,127.768,84,122.099,84c-5.468,0-5.397-1.788-8.783-4.626c-1.129-0.946-2.029-1.742-2.779-2.514				c-0.249,3.256-0.752,7.572-1.437,8.172l-10.432,6.291c3.911,2.136,11.363,5.01,23.582,5.427				c14.092,0.48,20.283-3.155,23.198-5.335C137.705,87.858,134.9,85.032,134.9,85.032z",
        "path://M88.01,126.492c0,0,0.01,0.076,0.023,0.171C88.019,126.561,88.01,126.492,88.01,126.492z				 M87.75,94.917c-0.019-0.057-0.071-0.157-0.149-0.29c1.097-0.092-2.202-0.059-5.199,0.948				c-4.025,1.417-8.266,5.413-11.037,12.738c-1.035,2.736-1.51,5.936-1.761,9.639c0,2.919-0.023,6.811-1.866,13.621				c-1.843,3.892-5.529,15.567-6.45,24.323c-0.921,3.892-4.607,11.676-5.528,15.567c-0.921,2.919-7.19,29.682-10.778,52.059				c0,3.892-7.395,7.783-8.316,9.729s-2.921,7.507-5.186,10.257c-0.319,0.387-0.823,2.312,0.518,2.218				c1.122-0.079,3.91-1.945,5.589-3.718c0,0-2.364,11.675-2.364,15.566c0,0,0.521,4.864,2.364,0l2.764-10.702				c0,0,0.921-0.973,0,7.783c0,2.919-1.321,10.702,2.364,5.838c0,0,2.243-9.729,2.243-12.648c0,0,0.922-1.945,0.922,2.919				c0,0-0.922,11.675,1.843,10.702c0.921-0.973,0.921-9.729,1.843-13.621c0,0,0.921-1.945,0.921,2.919				c0,2.919,1.8,12.275,2.764,3.892c-0.079-2.746,1.164-8.423,1.843-25.936c0-2.919,1.25-7.7,4.936-13.538				c1.843-2.919,13.136-30.098,13.136-39.827c0-3.892,14.844-39.134,14.844-48.863C86.609,118.001,91.359,105.743,87.75,94.917z				 M212.27,244.259c-1.764-1.75-5.014-9.061-5.936-11.007s-8.315-5.838-8.315-9.729c-2.255-21.044-8.857-49.14-9.778-52.059				c-0.922-3.892-4.607-11.676-5.529-15.567c-0.921-8.756-4.606-20.432-6.449-24.323c-1.843-6.811-1.866-10.702-1.866-13.621				c-0.229-3.394-0.608-6.364-1.508-8.942c-2.712-7.77-7.14-11.974-11.29-13.435c-1.994-0.67-3.841-0.987-5.571-1.227				c-0.298,0.751-0.671,1.629-0.999,2.498c0.086-0.187-0.565,2.076-0.15,7.18c0.543,6.646,2.213,17.229,1.113,22.466				c0,9.729,14.845,44.972,14.845,48.863c0,9.729,11.876,40.158,13.719,43.077c3.686,5.838,3.686,10.702,3.686,13.621				c1.278,17.513,0.588,19.057,1.509,22.603c1.765,7.983,2.765-0.973,2.765-3.892c0-4.864,0.922-2.919,0.922-2.919				c0.921,3.892,1.121,12.648,2.042,13.621c2.765,0.973,1.644-10.702,1.644-10.702c0-4.864,0.921-2.919,0.921-2.919				c0,2.919,2.243,12.648,2.243,12.648c3.686,4.864,2.364-2.919,2.364-5.838c-0.922-8.756,0-7.783,0-7.783l2.764,10.702				c1.844,4.864,2.164,0,2.164,0c0-3.892-2.164-15.566-2.164-15.566c1.129,1.191,2.521,3.425,3.543,3.851				C211.271,246.824,212.724,244.709,212.27,244.259z",
        "path://M154.877,104.026c-0.415-5.103,0.236-7.366,0.15-7.18c0.328-0.87,0.702-1.748,1-2.499				c-2.564-0.354-4.857-0.523-6.89-1.344c-1.339-0.541-2.565-1.072-3.689-1.589c-2.915,2.18-9.106,5.815-23.198,5.335				c-12.219-0.417-19.671-3.291-23.582-5.427c-1.649,0.771-3.513,1.578-5.637,2.408c-1.654,0.647-3.465,0.73-5.43,0.896				c0.078,0.133,0.13,0.233,0.149,0.29c3.609,10.826-1.141,23.084,0.26,31.575c0,0,2.371,17.832,4.741,28.982				c4.221-1.504,10.153-3.523,14.706-4.374c4.68-0.875,9.983-1.627,15.293-1.351c7.18,0.373,13.266,1.431,17.996,2.335				c3.427,0.655,7.604,1.335,10.58,1.815c1.916-11.175,4.664-27.408,4.664-27.408C157.09,121.256,155.42,110.673,154.877,104.026z",
        "path://M155.328,193.896c-0.922-6.811-6.666-24.579-4.866-34.979c0.254-1.468,0.549-3.182,0.864-5.017				c-2.976-0.48-7.153-1.16-10.58-1.815c-4.73-0.904-10.816-1.962-17.996-2.335c-5.31-0.276-10.613,0.476-15.293,1.351				c-10.072,1.882-14.755,4.496-14.724,4.394c-0.013,0.038,0.494,2.179,0.805,3.423c2.6,10.399-3.944,28.168-4.866,34.979				c-0.088,0.279-0.201,0.859-0.332,1.688c3.913,1.58,16.325,5.753,36.16,4.666c17.666-0.968,27.332-2.77,31.307-3.685				C155.611,195.203,155.447,194.274,155.328,193.896z",
        "path://M159.268,211.039c-0.441-4.467-1.216-8.439-1.735-10.794c0.19-0.086,0.356-0.169,0.468-0.245				c0.864-0.588,0-4,0-4s-9.166,2.917-33.5,4.25C100.167,201.583,87,195,87,195s-0.667,1.667-1,3				c-0.054,0.214,0.171,0.455,0.607,0.713c-0.681,3.063-2.089,9.854-2.922,17.071c-1.336,11.574-0.92,17.69-0.185,18.216				c2.333,1.667,36.167,4.75,37.166,2.75c1-2,1-2,1-2l2.043-0.167L124,235.75c0,0,0.666,3,12,2s24.804-2.678,24.75-5.75				C160.689,228.563,160.285,221.326,159.268,211.039z",
        "path://M154.25,321.191c-1.844-5.838-0.922-16.54,0-20.432c3.686-15.567,5.527-32.976,5.527-46.597				c0-6.154-0.268-13.261-0.674-20.391c-3.922,1.98-14.174,3.189-23.104,3.978c-8.324,0.734-10.892-0.688-11.67-1.496				c1.333,19.9,4.12,30.385,4.12,37.155c0,21.404,5.528,44.863,5.528,53.619c0,8.757-6.084,21.297-2.741,37.161				c3.686,8.757,2.741,49.075,2.741,50.048c-0.922,6.811-2.764,8.757-2.764,10.703c0,1.945,2.764,4.864,0.921,7.783				s0,7.783,1.843,9.729c1.843,1.945,5.528-1.946,13.82-0.974c8.293,0.974,5.529-3.892,5.529-3.892				c-3.686-0.973-5.529-5.837-5.529-5.837s0.922-6.811,0-8.757c-2.764-8.757,2.766-51.021,4.607-55.886				C157.936,354.46,156.092,328.002,154.25,321.191z M84.857,234.48c-0.384,6.892-0.635,13.732-0.635,19.683				c0,13.621,1.843,31.029,5.529,46.597c0.921,3.892,1.843,14.594,0,20.432c-1.843,6.811-3.686,33.269,1.843,45.917				c1.843,4.864,7.371,47.129,4.607,55.886c-0.921,1.946,0,8.757,0,8.757s-1.843,4.864-5.528,5.837c0,0-2.765,4.865,5.528,3.892				c8.292-0.973,11.978,2.919,13.821,0.974c1.843-1.946,3.686-6.811,1.843-9.729c-1.843-2.919,0.921-5.838,0.921-7.783				c0-1.946-1.843-3.893-2.765-10.703c0-0.973-0.944-41.291,2.742-50.048c1.843-17.364-2.742-28.404-2.742-37.161				c0-8.756,5.529-32.215,5.529-53.619c0-6.662,2.696-16.941,4.052-36.231C114.286,238.093,90.977,236.009,84.857,234.48z",
        "path://M90.652,31H90C90,31,90.179,31.368,90.652,31z M152,38c-0.584-3.417-1.834-9.667-3-11				c0.916,0.083,3-1,3-1s-1.084-1.333-3-2s-7.334-7.333-10-10s-8-4-8-4s0.311-0.771,1-1c0.802-0.268,2,0,2,0s-1.584-1-4-1				c-1.609,0-2.847,1.771-3.495,2.957C125.859,9.718,124.62,7.727,123,7c-2.416-1.083-3-1-3-1s1.75,2.083,2,3c0,0-1.5-1.167-5-1				c-1.497,0.071-2.445,0.265-3.04,0.462C113.164,8.727,113,9,113,9h1c0,0-4.333,0.417-8,2s-9,6-9,6h2c0,0-3.167,4.083-4,5				c-2.167,0.333-2,0-2,0s-0.417,0.417,1,1s1,1,1,1s-1.667,2.333-3,5c-0.6,1.2-1.037,1.758-1.348,2H92c0,0-0.004,1.607-1,5				c-0.714,2.434-0.287,8.385,0,9c0,0,0.75-4.083,1-3s-0.167,1.583,0,4s1,3,1,3l1-2c0,0,0.917,4.5,1,6s0,2,0,2l1-2v1l0.569-0.854				c0.529,0.498,1.135,0.37,1.635,0.552c-1.379,3.658,1.457,7.579,3.234,10.255c1.909,2.873,3.84,2.011,3.84,2.011l-0.079-0.541				c0.178,0.653,0.354,1.271,0.523,1.804c2.219,6.977,3.079,8.363,7.593,12.147c3.386,2.838,3.315,4.626,8.783,4.626				c5.669,0,5.154-1.94,8.896-4.974c3.35-2.716,5.586-4.172,7.426-10.634c0.188-0.662,0.404-1.426,0.631-2.23				c0.561,0.123,2.009,0.171,3.472-2.062c1.644-2.51,4.214-6.127,3.333-9.58c0.46-0.254,1.039-0.327,1.322-1.365				c0.053-0.192,0.109-0.4,0.164-0.602C148.352,51.021,150,47,150,47v1.754c0.115-0.262,0.385-0.763,1-1.754c1.5-2.417,0-7,0-7l1,2				C152,42,152.584,41.417,152,38z M122.072,9.264C122.137,9.5,122.109,9.402,122.072,9.264L122.072,9.264z",
        "path://M134.9,85.032c-0.596-0.533-0.982-4.968-1.021-8.421c-0.864,0.812-1.817,1.55-2.885,2.415				C127.253,82.06,127.768,84,122.099,84c-5.468,0-5.397-1.788-8.783-4.626c-1.129-0.946-2.029-1.742-2.779-2.514				c-0.249,3.256-0.752,7.572-1.437,8.172l-10.432,6.291c3.911,2.136,11.363,5.01,23.582,5.427				c14.092,0.48,20.283-3.155,23.198-5.335C137.705,87.858,134.9,85.032,134.9,85.032z",
        "path://M88.01,126.492c0,0,0.01,0.076,0.023,0.171C88.019,126.561,88.01,126.492,88.01,126.492z				 M87.75,94.917c-0.019-0.057-0.071-0.157-0.149-0.29c1.097-0.092-2.202-0.059-5.199,0.948				c-4.025,1.417-8.266,5.413-11.037,12.738c-1.035,2.736-1.51,5.936-1.761,9.639c0,2.919-0.023,6.811-1.866,13.621				c-1.843,3.892-5.529,15.567-6.45,24.323c-0.921,3.892-4.607,11.676-5.528,15.567c-0.921,2.919-7.19,29.682-10.778,52.059				c0,3.892-7.395,7.783-8.316,9.729s-2.921,7.507-5.186,10.257c-0.319,0.387-0.823,2.312,0.518,2.218				c1.122-0.079,3.91-1.945,5.589-3.718c0,0-2.364,11.675-2.364,15.566c0,0,0.521,4.864,2.364,0l2.764-10.702				c0,0,0.921-0.973,0,7.783c0,2.919-1.321,10.702,2.364,5.838c0,0,2.243-9.729,2.243-12.648c0,0,0.922-1.945,0.922,2.919				c0,0-0.922,11.675,1.843,10.702c0.921-0.973,0.921-9.729,1.843-13.621c0,0,0.921-1.945,0.921,2.919				c0,2.919,1.8,12.275,2.764,3.892c-0.079-2.746,1.164-8.423,1.843-25.936c0-2.919,1.25-7.7,4.936-13.538				c1.843-2.919,13.136-30.098,13.136-39.827c0-3.892,14.844-39.134,14.844-48.863C86.609,118.001,91.359,105.743,87.75,94.917z				 M212.27,244.259c-1.764-1.75-5.014-9.061-5.936-11.007s-8.315-5.838-8.315-9.729c-2.255-21.044-8.857-49.14-9.778-52.059				c-0.922-3.892-4.607-11.676-5.529-15.567c-0.921-8.756-4.606-20.432-6.449-24.323c-1.843-6.811-1.866-10.702-1.866-13.621				c-0.229-3.394-0.608-6.364-1.508-8.942c-2.712-7.77-7.14-11.974-11.29-13.435c-1.994-0.67-3.841-0.987-5.571-1.227				c-0.298,0.751-0.671,1.629-0.999,2.498c0.086-0.187-0.565,2.076-0.15,7.18c0.543,6.646,2.213,17.229,1.113,22.466				c0,9.729,14.845,44.972,14.845,48.863c0,9.729,11.876,40.158,13.719,43.077c3.686,5.838,3.686,10.702,3.686,13.621				c1.278,17.513,0.588,19.057,1.509,22.603c1.765,7.983,2.765-0.973,2.765-3.892c0-4.864,0.922-2.919,0.922-2.919				c0.921,3.892,1.121,12.648,2.042,13.621c2.765,0.973,1.644-10.702,1.644-10.702c0-4.864,0.921-2.919,0.921-2.919				c0,2.919,2.243,12.648,2.243,12.648c3.686,4.864,2.364-2.919,2.364-5.838c-0.922-8.756,0-7.783,0-7.783l2.764,10.702				c1.844,4.864,2.164,0,2.164,0c0-3.892-2.164-15.566-2.164-15.566c1.129,1.191,2.521,3.425,3.543,3.851				C211.271,246.824,212.724,244.709,212.27,244.259z",
        "path://M154.877,104.026c-0.415-5.103,0.236-7.366,0.15-7.18c0.328-0.87,0.702-1.748,1-2.499				c-2.564-0.354-4.857-0.523-6.89-1.344c-1.339-0.541-2.565-1.072-3.689-1.589c-2.915,2.18-9.106,5.815-23.198,5.335				c-12.219-0.417-19.671-3.291-23.582-5.427c-1.649,0.771-3.513,1.578-5.637,2.408c-1.654,0.647-3.465,0.73-5.43,0.896				c0.078,0.133,0.13,0.233,0.149,0.29c3.609,10.826-1.141,23.084,0.26,31.575c0,0,2.371,17.832,4.741,28.982				c4.221-1.504,10.153-3.523,14.706-4.374c4.68-0.875,9.983-1.627,15.293-1.351c7.18,0.373,13.266,1.431,17.996,2.335				c3.427,0.655,7.604,1.335,10.58,1.815c1.916-11.175,4.664-27.408,4.664-27.408C157.09,121.256,155.42,110.673,154.877,104.026z",
        "path://M155.328,193.896c-0.922-6.811-6.666-24.579-4.866-34.979c0.254-1.468,0.549-3.182,0.864-5.017				c-2.976-0.48-7.153-1.16-10.58-1.815c-4.73-0.904-10.816-1.962-17.996-2.335c-5.31-0.276-10.613,0.476-15.293,1.351				c-10.072,1.882-14.755,4.496-14.724,4.394c-0.013,0.038,0.494,2.179,0.805,3.423c2.6,10.399-3.944,28.168-4.866,34.979				c-0.088,0.279-0.201,0.859-0.332,1.688c3.913,1.58,16.325,5.753,36.16,4.666c17.666-0.968,27.332-2.77,31.307-3.685				C155.611,195.203,155.447,194.274,155.328,193.896z",
        "path://M159.268,211.039c-0.441-4.467-1.216-8.439-1.735-10.794c0.19-0.086,0.356-0.169,0.468-0.245				c0.864-0.588,0-4,0-4s-9.166,2.917-33.5,4.25C100.167,201.583,87,195,87,195s-0.667,1.667-1,3				c-0.054,0.214,0.171,0.455,0.607,0.713c-0.681,3.063-2.089,9.854-2.922,17.071c-1.336,11.574-0.92,17.69-0.185,18.216				c2.333,1.667,36.167,4.75,37.166,2.75c1-2,1-2,1-2l2.043-0.167L124,235.75c0,0,0.666,3,12,2s24.804-2.678,24.75-5.75				C160.689,228.563,160.285,221.326,159.268,211.039z",
        "path://M154.25,321.191c-1.844-5.838-0.922-16.54,0-20.432c3.686-15.567,5.527-32.976,5.527-46.597				c0-6.154-0.268-13.261-0.674-20.391c-3.922,1.98-14.174,3.189-23.104,3.978c-8.324,0.734-10.892-0.688-11.67-1.496				c1.333,19.9,4.12,30.385,4.12,37.155c0,21.404,5.528,44.863,5.528,53.619c0,8.757-6.084,21.297-2.741,37.161				c3.686,8.757,2.741,49.075,2.741,50.048c-0.922,6.811-2.764,8.757-2.764,10.703c0,1.945,2.764,4.864,0.921,7.783				s0,7.783,1.843,9.729c1.843,1.945,5.528-1.946,13.82-0.974c8.293,0.974,5.529-3.892,5.529-3.892				c-3.686-0.973-5.529-5.837-5.529-5.837s0.922-6.811,0-8.757c-2.764-8.757,2.766-51.021,4.607-55.886				C157.936,354.46,156.092,328.002,154.25,321.191z M84.857,234.48c-0.384,6.892-0.635,13.732-0.635,19.683				c0,13.621,1.843,31.029,5.529,46.597c0.921,3.892,1.843,14.594,0,20.432c-1.843,6.811-3.686,33.269,1.843,45.917				c1.843,4.864,7.371,47.129,4.607,55.886c-0.921,1.946,0,8.757,0,8.757s-1.843,4.864-5.528,5.837c0,0-2.765,4.865,5.528,3.892				c8.292-0.973,11.978,2.919,13.821,0.974c1.843-1.946,3.686-6.811,1.843-9.729c-1.843-2.919,0.921-5.838,0.921-7.783				c0-1.946-1.843-3.893-2.765-10.703c0-0.973-0.944-41.291,2.742-50.048c1.843-17.364-2.742-28.404-2.742-37.161				c0-8.756,5.529-32.215,5.529-53.619c0-6.662,2.696-16.941,4.052-36.231C114.286,238.093,90.977,236.009,84.857,234.48z",
        "path://M90.652,31H90C90,31,90.179,31.368,90.652,31z M152,38c-0.584-3.417-1.834-9.667-3-11				c0.916,0.083,3-1,3-1s-1.084-1.333-3-2s-7.334-7.333-10-10s-8-4-8-4s0.311-0.771,1-1c0.802-0.268,2,0,2,0s-1.584-1-4-1				c-1.609,0-2.847,1.771-3.495,2.957C125.859,9.718,124.62,7.727,123,7c-2.416-1.083-3-1-3-1s1.75,2.083,2,3c0,0-1.5-1.167-5-1				c-1.497,0.071-2.445,0.265-3.04,0.462C113.164,8.727,113,9,113,9h1c0,0-4.333,0.417-8,2s-9,6-9,6h2c0,0-3.167,4.083-4,5				c-2.167,0.333-2,0-2,0s-0.417,0.417,1,1s1,1,1,1s-1.667,2.333-3,5c-0.6,1.2-1.037,1.758-1.348,2H92c0,0-0.004,1.607-1,5				c-0.714,2.434-0.287,8.385,0,9c0,0,0.75-4.083,1-3s-0.167,1.583,0,4s1,3,1,3l1-2c0,0,0.917,4.5,1,6s0,2,0,2l1-2v1l0.569-0.854				c0.529,0.498,1.135,0.37,1.635,0.552c-1.379,3.658,1.457,7.579,3.234,10.255c1.909,2.873,3.84,2.011,3.84,2.011l-0.079-0.541				c0.178,0.653,0.354,1.271,0.523,1.804c2.219,6.977,3.079,8.363,7.593,12.147c3.386,2.838,3.315,4.626,8.783,4.626				c5.669,0,5.154-1.94,8.896-4.974c3.35-2.716,5.586-4.172,7.426-10.634c0.188-0.662,0.404-1.426,0.631-2.23				c0.561,0.123,2.009,0.171,3.472-2.062c1.644-2.51,4.214-6.127,3.333-9.58c0.46-0.254,1.039-0.327,1.322-1.365				c0.053-0.192,0.109-0.4,0.164-0.602C148.352,51.021,150,47,150,47v1.754c0.115-0.262,0.385-0.763,1-1.754c1.5-2.417,0-7,0-7l1,2				C152,42,152.584,41.417,152,38z M122.072,9.264C122.137,9.5,122.109,9.402,122.072,9.264L122.072,9.264z",
        "path://M134.9,85.032c-0.596-0.533-0.982-4.968-1.021-8.421c-0.864,0.812-1.817,1.55-2.885,2.415				C127.253,82.06,127.768,84,122.099,84c-5.468,0-5.397-1.788-8.783-4.626c-1.129-0.946-2.029-1.742-2.779-2.514				c-0.249,3.256-0.752,7.572-1.437,8.172l-10.432,6.291c3.911,2.136,11.363,5.01,23.582,5.427				c14.092,0.48,20.283-3.155,23.198-5.335C137.705,87.858,134.9,85.032,134.9,85.032z",
        "path://M88.01,126.492c0,0,0.01,0.076,0.023,0.171C88.019,126.561,88.01,126.492,88.01,126.492z				 M87.75,94.917c-0.019-0.057-0.071-0.157-0.149-0.29c1.097-0.092-2.202-0.059-5.199,0.948				c-4.025,1.417-8.266,5.413-11.037,12.738c-1.035,2.736-1.51,5.936-1.761,9.639c0,2.919-0.023,6.811-1.866,13.621				c-1.843,3.892-5.529,15.567-6.45,24.323c-0.921,3.892-4.607,11.676-5.528,15.567c-0.921,2.919-7.19,29.682-10.778,52.059				c0,3.892-7.395,7.783-8.316,9.729s-2.921,7.507-5.186,10.257c-0.319,0.387-0.823,2.312,0.518,2.218				c1.122-0.079,3.91-1.945,5.589-3.718c0,0-2.364,11.675-2.364,15.566c0,0,0.521,4.864,2.364,0l2.764-10.702				c0,0,0.921-0.973,0,7.783c0,2.919-1.321,10.702,2.364,5.838c0,0,2.243-9.729,2.243-12.648c0,0,0.922-1.945,0.922,2.919				c0,0-0.922,11.675,1.843,10.702c0.921-0.973,0.921-9.729,1.843-13.621c0,0,0.921-1.945,0.921,2.919				c0,2.919,1.8,12.275,2.764,3.892c-0.079-2.746,1.164-8.423,1.843-25.936c0-2.919,1.25-7.7,4.936-13.538				c1.843-2.919,13.136-30.098,13.136-39.827c0-3.892,14.844-39.134,14.844-48.863C86.609,118.001,91.359,105.743,87.75,94.917z				 M212.27,244.259c-1.764-1.75-5.014-9.061-5.936-11.007s-8.315-5.838-8.315-9.729c-2.255-21.044-8.857-49.14-9.778-52.059				c-0.922-3.892-4.607-11.676-5.529-15.567c-0.921-8.756-4.606-20.432-6.449-24.323c-1.843-6.811-1.866-10.702-1.866-13.621				c-0.229-3.394-0.608-6.364-1.508-8.942c-2.712-7.77-7.14-11.974-11.29-13.435c-1.994-0.67-3.841-0.987-5.571-1.227				c-0.298,0.751-0.671,1.629-0.999,2.498c0.086-0.187-0.565,2.076-0.15,7.18c0.543,6.646,2.213,17.229,1.113,22.466				c0,9.729,14.845,44.972,14.845,48.863c0,9.729,11.876,40.158,13.719,43.077c3.686,5.838,3.686,10.702,3.686,13.621				c1.278,17.513,0.588,19.057,1.509,22.603c1.765,7.983,2.765-0.973,2.765-3.892c0-4.864,0.922-2.919,0.922-2.919				c0.921,3.892,1.121,12.648,2.042,13.621c2.765,0.973,1.644-10.702,1.644-10.702c0-4.864,0.921-2.919,0.921-2.919				c0,2.919,2.243,12.648,2.243,12.648c3.686,4.864,2.364-2.919,2.364-5.838c-0.922-8.756,0-7.783,0-7.783l2.764,10.702				c1.844,4.864,2.164,0,2.164,0c0-3.892-2.164-15.566-2.164-15.566c1.129,1.191,2.521,3.425,3.543,3.851				C211.271,246.824,212.724,244.709,212.27,244.259z",
        "path://M154.877,104.026c-0.415-5.103,0.236-7.366,0.15-7.18c0.328-0.87,0.702-1.748,1-2.499				c-2.564-0.354-4.857-0.523-6.89-1.344c-1.339-0.541-2.565-1.072-3.689-1.589c-2.915,2.18-9.106,5.815-23.198,5.335				c-12.219-0.417-19.671-3.291-23.582-5.427c-1.649,0.771-3.513,1.578-5.637,2.408c-1.654,0.647-3.465,0.73-5.43,0.896				c0.078,0.133,0.13,0.233,0.149,0.29c3.609,10.826-1.141,23.084,0.26,31.575c0,0,2.371,17.832,4.741,28.982				c4.221-1.504,10.153-3.523,14.706-4.374c4.68-0.875,9.983-1.627,15.293-1.351c7.18,0.373,13.266,1.431,17.996,2.335				c3.427,0.655,7.604,1.335,10.58,1.815c1.916-11.175,4.664-27.408,4.664-27.408C157.09,121.256,155.42,110.673,154.877,104.026z",
        "path://M155.328,193.896c-0.922-6.811-6.666-24.579-4.866-34.979c0.254-1.468,0.549-3.182,0.864-5.017				c-2.976-0.48-7.153-1.16-10.58-1.815c-4.73-0.904-10.816-1.962-17.996-2.335c-5.31-0.276-10.613,0.476-15.293,1.351				c-10.072,1.882-14.755,4.496-14.724,4.394c-0.013,0.038,0.494,2.179,0.805,3.423c2.6,10.399-3.944,28.168-4.866,34.979				c-0.088,0.279-0.201,0.859-0.332,1.688c3.913,1.58,16.325,5.753,36.16,4.666c17.666-0.968,27.332-2.77,31.307-3.685				C155.611,195.203,155.447,194.274,155.328,193.896z",
        "path://M159.268,211.039c-0.441-4.467-1.216-8.439-1.735-10.794c0.19-0.086,0.356-0.169,0.468-0.245				c0.864-0.588,0-4,0-4s-9.166,2.917-33.5,4.25C100.167,201.583,87,195,87,195s-0.667,1.667-1,3				c-0.054,0.214,0.171,0.455,0.607,0.713c-0.681,3.063-2.089,9.854-2.922,17.071c-1.336,11.574-0.92,17.69-0.185,18.216				c2.333,1.667,36.167,4.75,37.166,2.75c1-2,1-2,1-2l2.043-0.167L124,235.75c0,0,0.666,3,12,2s24.804-2.678,24.75-5.75				C160.689,228.563,160.285,221.326,159.268,211.039zM154.25,321.191c-1.844-5.838-0.922-16.54,0-20.432c3.686-15.567,5.527-32.976,5.527-46.597c0-6.154-0.268-13.261-0.674-20.391c-3.922,1.98-14.174,3.189-23.104,3.978c-8.324,0.734-10.892-0.688-11.67-1.496				c1.333,19.9,4.12,30.385,4.12,37.155c0,21.404,5.528,44.863,5.528,53.619c0,8.757-6.084,21.297-2.741,37.161				c3.686,8.757,2.741,49.075,2.741,50.048c-0.922,6.811-2.764,8.757-2.764,10.703c0,1.945,2.764,4.864,0.921,7.783				s0,7.783,1.843,9.729c1.843,1.945,5.528-1.946,13.82-0.974c8.293,0.974,5.529-3.892,5.529-3.892				c-3.686-0.973-5.529-5.837-5.529-5.837s0.922-6.811,0-8.757c-2.764-8.757,2.766-51.021,4.607-55.886				C157.936,354.46,156.092,328.002,154.25,321.191z M84.857,234.48c-0.384,6.892-0.635,13.732-0.635,19.683c0,13.621,1.843,31.029,5.529,46.597c0.921,3.892,1.843,14.594,0,20.432c-1.843,6.811-3.686,33.269,1.843,45.917				c1.843,4.864,7.371,47.129,4.607,55.886c-0.921,1.946,0,8.757,0,8.757s-1.843,4.864-5.528,5.837c0,0-2.765,4.865,5.528,3.892				c8.292-0.973,11.978,2.919,13.821,0.974c1.843-1.946,3.686-6.811,1.843-9.729c-1.843-2.919,0.921-5.838,0.921-7.783				c0-1.946-1.843-3.893-2.765-10.703c0-0.973-0.944-41.291,2.742-50.048c1.843-17.364-2.742-28.404-2.742-37.161				c0-8.756,5.529-32.215,5.529-53.619c0-6.662,2.696-16.941,4.052-36.231C114.286,238.093,90.977,236.009,84.857,234.48z",
        "path://M90.652,31H90C90,31,90.179,31.368,90.652,31z M152,38c-0.584-3.417-1.834-9.667-3-11				c0.916,0.083,3-1,3-1s-1.084-1.333-3-2s-7.334-7.333-10-10s-8-4-8-4s0.311-0.771,1-1c0.802-0.268,2,0,2,0s-1.584-1-4-1				c-1.609,0-2.847,1.771-3.495,2.957C125.859,9.718,124.62,7.727,123,7c-2.416-1.083-3-1-3-1s1.75,2.083,2,3c0,0-1.5-1.167-5-1				c-1.497,0.071-2.445,0.265-3.04,0.462C113.164,8.727,113,9,113,9h1c0,0-4.333,0.417-8,2s-9,6-9,6h2c0,0-3.167,4.083-4,5				c-2.167,0.333-2,0-2,0s-0.417,0.417,1,1s1,1,1,1s-1.667,2.333-3,5c-0.6,1.2-1.037,1.758-1.348,2H92c0,0-0.004,1.607-1,5				c-0.714,2.434-0.287,8.385,0,9c0,0,0.75-4.083,1-3s-0.167,1.583,0,4s1,3,1,3l1-2c0,0,0.917,4.5,1,6s0,2,0,2l1-2v1l0.569-0.854				c0.529,0.498,1.135,0.37,1.635,0.552c-1.379,3.658,1.457,7.579,3.234,10.255c1.909,2.873,3.84,2.011,3.84,2.011l-0.079-0.541				c0.178,0.653,0.354,1.271,0.523,1.804c2.219,6.977,3.079,8.363,7.593,12.147c3.386,2.838,3.315,4.626,8.783,4.626				c5.669,0,5.154-1.94,8.896-4.974c3.35-2.716,5.586-4.172,7.426-10.634c0.188-0.662,0.404-1.426,0.631-2.23				c0.561,0.123,2.009,0.171,3.472-2.062c1.644-2.51,4.214-6.127,3.333-9.58c0.46-0.254,1.039-0.327,1.322-1.365				c0.053-0.192,0.109-0.4,0.164-0.602C148.352,51.021,150,47,150,47v1.754c0.115-0.262,0.385-0.763,1-1.754c1.5-2.417,0-7,0-7l1,2				C152,42,152.584,41.417,152,38z M122.072,9.264C122.137,9.5,122.109,9.402,122.072,9.264L122.072,9.264z",
        "path://M134.9,85.032c-0.596-0.533-0.982-4.968-1.021-8.421c-0.864,0.812-1.817,1.55-2.885,2.415				C127.253,82.06,127.768,84,122.099,84c-5.468,0-5.397-1.788-8.783-4.626c-1.129-0.946-2.029-1.742-2.779-2.514				c-0.249,3.256-0.752,7.572-1.437,8.172l-10.432,6.291c3.911,2.136,11.363,5.01,23.582,5.427				c14.092,0.48,20.283-3.155,23.198-5.335C137.705,87.858,134.9,85.032,134.9,85.032z",
        "path://M88.01,126.492c0,0,0.01,0.076,0.023,0.171C88.019,126.561,88.01,126.492,88.01,126.492z				 M87.75,94.917c-0.019-0.057-0.071-0.157-0.149-0.29c1.097-0.092-2.202-0.059-5.199,0.948				c-4.025,1.417-8.266,5.413-11.037,12.738c-1.035,2.736-1.51,5.936-1.761,9.639c0,2.919-0.023,6.811-1.866,13.621				c-1.843,3.892-5.529,15.567-6.45,24.323c-0.921,3.892-4.607,11.676-5.528,15.567c-0.921,2.919-7.19,29.682-10.778,52.059				c0,3.892-7.395,7.783-8.316,9.729s-2.921,7.507-5.186,10.257c-0.319,0.387-0.823,2.312,0.518,2.218				c1.122-0.079,3.91-1.945,5.589-3.718c0,0-2.364,11.675-2.364,15.566c0,0,0.521,4.864,2.364,0l2.764-10.702				c0,0,0.921-0.973,0,7.783c0,2.919-1.321,10.702,2.364,5.838c0,0,2.243-9.729,2.243-12.648c0,0,0.922-1.945,0.922,2.919				c0,0-0.922,11.675,1.843,10.702c0.921-0.973,0.921-9.729,1.843-13.621c0,0,0.921-1.945,0.921,2.919				c0,2.919,1.8,12.275,2.764,3.892c-0.079-2.746,1.164-8.423,1.843-25.936c0-2.919,1.25-7.7,4.936-13.538				c1.843-2.919,13.136-30.098,13.136-39.827c0-3.892,14.844-39.134,14.844-48.863C86.609,118.001,91.359,105.743,87.75,94.917z				 M212.27,244.259c-1.764-1.75-5.014-9.061-5.936-11.007s-8.315-5.838-8.315-9.729c-2.255-21.044-8.857-49.14-9.778-52.059				c-0.922-3.892-4.607-11.676-5.529-15.567c-0.921-8.756-4.606-20.432-6.449-24.323c-1.843-6.811-1.866-10.702-1.866-13.621				c-0.229-3.394-0.608-6.364-1.508-8.942c-2.712-7.77-7.14-11.974-11.29-13.435c-1.994-0.67-3.841-0.987-5.571-1.227				c-0.298,0.751-0.671,1.629-0.999,2.498c0.086-0.187-0.565,2.076-0.15,7.18c0.543,6.646,2.213,17.229,1.113,22.466				c0,9.729,14.845,44.972,14.845,48.863c0,9.729,11.876,40.158,13.719,43.077c3.686,5.838,3.686,10.702,3.686,13.621				c1.278,17.513,0.588,19.057,1.509,22.603c1.765,7.983,2.765-0.973,2.765-3.892c0-4.864,0.922-2.919,0.922-2.919				c0.921,3.892,1.121,12.648,2.042,13.621c2.765,0.973,1.644-10.702,1.644-10.702c0-4.864,0.921-2.919,0.921-2.919				c0,2.919,2.243,12.648,2.243,12.648c3.686,4.864,2.364-2.919,2.364-5.838c-0.922-8.756,0-7.783,0-7.783l2.764,10.702				c1.844,4.864,2.164,0,2.164,0c0-3.892-2.164-15.566-2.164-15.566c1.129,1.191,2.521,3.425,3.543,3.851				C211.271,246.824,212.724,244.709,212.27,244.259z",
        "path://M154.877,104.026c-0.415-5.103,0.236-7.366,0.15-7.18c0.328-0.87,0.702-1.748,1-2.499				c-2.564-0.354-4.857-0.523-6.89-1.344c-1.339-0.541-2.565-1.072-3.689-1.589c-2.915,2.18-9.106,5.815-23.198,5.335				c-12.219-0.417-19.671-3.291-23.582-5.427c-1.649,0.771-3.513,1.578-5.637,2.408c-1.654,0.647-3.465,0.73-5.43,0.896				c0.078,0.133,0.13,0.233,0.149,0.29c3.609,10.826-1.141,23.084,0.26,31.575c0,0,2.371,17.832,4.741,28.982				c4.221-1.504,10.153-3.523,14.706-4.374c4.68-0.875,9.983-1.627,15.293-1.351c7.18,0.373,13.266,1.431,17.996,2.335				c3.427,0.655,7.604,1.335,10.58,1.815c1.916-11.175,4.664-27.408,4.664-27.408C157.09,121.256,155.42,110.673,154.877,104.026z",
        "path://M155.328,193.896c-0.922-6.811-6.666-24.579-4.866-34.979c0.254-1.468,0.549-3.182,0.864-5.017				c-2.976-0.48-7.153-1.16-10.58-1.815c-4.73-0.904-10.816-1.962-17.996-2.335c-5.31-0.276-10.613,0.476-15.293,1.351				c-10.072,1.882-14.755,4.496-14.724,4.394c-0.013,0.038,0.494,2.179,0.805,3.423c2.6,10.399-3.944,28.168-4.866,34.979				c-0.088,0.279-0.201,0.859-0.332,1.688c3.913,1.58,16.325,5.753,36.16,4.666c17.666-0.968,27.332-2.77,31.307-3.685				C155.611,195.203,155.447,194.274,155.328,193.896z",
        "path://M159.268,211.039c-0.441-4.467-1.216-8.439-1.735-10.794c0.19-0.086,0.356-0.169,0.468-0.245				c0.864-0.588,0-4,0-4s-9.166,2.917-33.5,4.25C100.167,201.583,87,195,87,195s-0.667,1.667-1,3				c-0.054,0.214,0.171,0.455,0.607,0.713c-0.681,3.063-2.089,9.854-2.922,17.071c-1.336,11.574-0.92,17.69-0.185,18.216				c2.333,1.667,36.167,4.75,37.166,2.75c1-2,1-2,1-2l2.043-0.167L124,235.75c0,0,0.666,3,12,2s24.804-2.678,24.75-5.75				C160.689,228.563,160.285,221.326,159.268,211.039zM154.25,321.191c-1.844-5.838-0.922-16.54,0-20.432c3.686-15.567,5.527-32.976,5.527-46.597c0-6.154-0.268-13.261-0.674-20.391c-3.922,1.98-14.174,3.189-23.104,3.978c-8.324,0.734-10.892-0.688-11.67-1.496				c1.333,19.9,4.12,30.385,4.12,37.155c0,21.404,5.528,44.863,5.528,53.619c0,8.757-6.084,21.297-2.741,37.161				c3.686,8.757,2.741,49.075,2.741,50.048c-0.922,6.811-2.764,8.757-2.764,10.703c0,1.945,2.764,4.864,0.921,7.783				s0,7.783,1.843,9.729c1.843,1.945,5.528-1.946,13.82-0.974c8.293,0.974,5.529-3.892,5.529-3.892c-3.686-0.973-5.529-5.837-5.529-5.837s0.922-6.811,0-8.757c-2.764-8.757,2.766-51.021,4.607-55.886C157.936,354.46,156.092,328.002,154.25,321.191z M84.857,234.48c-0.384,6.892-0.635,13.732-0.635,19.683c0,13.621,1.843,31.029,5.529,46.597c0.921,3.892,1.843,14.594,0,20.432c-1.843,6.811-3.686,33.269,1.843,45.917				c1.843,4.864,7.371,47.129,4.607,55.886c-0.921,1.946,0,8.757,0,8.757s-1.843,4.864-5.528,5.837c0,0-2.765,4.865,5.528,3.892				c8.292-0.973,11.978,2.919,13.821,0.974c1.843-1.946,3.686-6.811,1.843-9.729c-1.843-2.919,0.921-5.838,0.921-7.783				c0-1.946-1.843-3.893-2.765-10.703c0-0.973-0.944-41.291,2.742-50.048c1.843-17.364-2.742-28.404-2.742-37.161				c0-8.756,5.529-32.215,5.529-53.619c0-6.662,2.696-16.941,4.052-36.231C114.286,238.093,90.977,236.009,84.857,234.48zM88.01,126.492c0,0,0.01,0.076,0.023,0.171C88.019,126.561,88.01,126.492,88.01,126.492z M87.75,94.917c-0.019-0.057-0.071-0.157-0.149-0.29c1.097-0.092-2.202-0.059-5.199,0.948c-4.025,1.417-8.266,5.413-11.037,12.738c-1.035,2.736-1.51,5.936-1.761,9.639c0,2.919-0.023,6.811-1.866,13.621c-1.843,3.892-5.529,15.567-6.45,24.323c-0.921,3.892-4.607,11.676-5.528,15.567c-0.921,2.919-7.19,29.682-10.778,52.059c0,3.892-7.395,7.783-8.316,9.729s-2.921,7.507-5.186,10.257c-0.319,0.387-0.823,2.312,0.518,2.218c1.122-0.079,3.91-1.945,5.589-3.718c0,0-2.364,11.675-2.364,15.566c0,0,0.521,4.864,2.364,0l2.764-10.702c0,0,0.921-0.973,0,7.783c0,2.919-1.321,10.702,2.364,5.838c0,0,2.243-9.729,2.243-12.648c0,0,0.922-1.945,0.922,2.919c0,0-0.922,11.675,1.843,10.702c0.921-0.973,0.921-9.729,1.843-13.621c0,0,0.921-1.945,0.921,2.919c0,2.919,1.8,12.275,2.764,3.892c-0.079-2.746,1.164-8.423,1.843-25.936c0-2.919,1.25-7.7,4.936-13.538c1.843-2.919,13.136-30.098,13.136-39.827c0-3.892,14.844-39.134,14.844-48.863C86.609,118.001,91.359,105.743,87.75,94.917z M212.27,244.259c-1.764-1.75-5.014-9.061-5.936-11.007s-8.315-5.838-8.315-9.729c-2.255-21.044-8.857-49.14-9.778-52.059				c-0.922-3.892-4.607-11.676-5.529-15.567c-0.921-8.756-4.606-20.432-6.449-24.323c-1.843-6.811-1.866-10.702-1.866-13.621				c-0.229-3.394-0.608-6.364-1.508-8.942c-2.712-7.77-7.14-11.974-11.29-13.435c-1.994-0.67-3.841-0.987-5.571-1.227				c-0.298,0.751-0.671,1.629-0.999,2.498c0.086-0.187-0.565,2.076-0.15,7.18c0.543,6.646,2.213,17.229,1.113,22.466				c0,9.729,14.845,44.972,14.845,48.863c0,9.729,11.876,40.158,13.719,43.077c3.686,5.838,3.686,10.702,3.686,13.621				c1.278,17.513,0.588,19.057,1.509,22.603c1.765,7.983,2.765-0.973,2.765-3.892c0-4.864,0.922-2.919,0.922-2.919				c0.921,3.892,1.121,12.648,2.042,13.621c2.765,0.973,1.644-10.702,1.644-10.702c0-4.864,0.921-2.919,0.921-2.919				c0,2.919,2.243,12.648,2.243,12.648c3.686,4.864,2.364-2.919,2.364-5.838c-0.922-8.756,0-7.783,0-7.783l2.764,10.702				c1.844,4.864,2.164,0,2.164,0c0-3.892-2.164-15.566-2.164-15.566c1.129,1.191,2.521,3.425,3.543,3.851				C211.271,246.824,212.724,244.709,212.27,244.259z",
        "path://M154.877,104.026c-0.415-5.103,0.236-7.366,0.15-7.18c0.328-0.87,0.702-1.748,1-2.499				c-2.564-0.354-4.857-0.523-6.89-1.344c-1.339-0.541-2.565-1.072-3.689-1.589c-2.915,2.18-9.106,5.815-23.198,5.335				c-12.219-0.417-19.671-3.291-23.582-5.427c-1.649,0.771-3.513,1.578-5.637,2.408c-1.654,0.647-3.465,0.73-5.43,0.896				c0.078,0.133,0.13,0.233,0.149,0.29c3.609,10.826-1.141,23.084,0.26,31.575c0,0,2.371,17.832,4.741,28.982				c4.221-1.504,10.153-3.523,14.706-4.374c4.68-0.875,9.983-1.627,15.293-1.351c7.18,0.373,13.266,1.431,17.996,2.335				c3.427,0.655,7.604,1.335,10.58,1.815c1.916-11.175,4.664-27.408,4.664-27.408C157.09,121.256,155.42,110.673,154.877,104.026z",
        "path://M155.328,193.896c-0.922-6.811-6.666-24.579-4.866-34.979c0.254-1.468,0.549-3.182,0.864-5.017				c-2.976-0.48-7.153-1.16-10.58-1.815c-4.73-0.904-10.816-1.962-17.996-2.335c-5.31-0.276-10.613,0.476-15.293,1.351				c-10.072,1.882-14.755,4.496-14.724,4.394c-0.013,0.038,0.494,2.179,0.805,3.423c2.6,10.399-3.944,28.168-4.866,34.979				c-0.088,0.279-0.201,0.859-0.332,1.688c3.913,1.58,16.325,5.753,36.16,4.666c17.666-0.968,27.332-2.77,31.307-3.685				C155.611,195.203,155.447,194.274,155.328,193.896z",
        "path://M159.268,211.039c-0.441-4.467-1.216-8.439-1.735-10.794c0.19-0.086,0.356-0.169,0.468-0.245				c0.864-0.588,0-4,0-4s-9.166,2.917-33.5,4.25C100.167,201.583,87,195,87,195s-0.667,1.667-1,3				c-0.054,0.214,0.171,0.455,0.607,0.713c-0.681,3.063-2.089,9.854-2.922,17.071c-1.336,11.574-0.92,17.69-0.185,18.216				c2.333,1.667,36.167,4.75,37.166,2.75c1-2,1-2,1-2l2.043-0.167L124,235.75c0,0,0.666,3,12,2s24.804-2.678,24.75-5.75				C160.689,228.563,160.285,221.326,159.268,211.039zM154.25,321.191c-1.844-5.838-0.922-16.54,0-20.432c3.686-15.567,5.527-32.976,5.527-46.597c0-6.154-0.268-13.261-0.674-20.391c-3.922,1.98-14.174,3.189-23.104,3.978c-8.324,0.734-10.892-0.688-11.67-1.496				c1.333,19.9,4.12,30.385,4.12,37.155c0,21.404,5.528,44.863,5.528,53.619c0,8.757-6.084,21.297-2.741,37.161				c3.686,8.757,2.741,49.075,2.741,50.048c-0.922,6.811-2.764,8.757-2.764,10.703c0,1.945,2.764,4.864,0.921,7.783				s0,7.783,1.843,9.729c1.843,1.945,5.528-1.946,13.82-0.974c8.293,0.974,5.529-3.892,5.529-3.892c-3.686-0.973-5.529-5.837-5.529-5.837s0.922-6.811,0-8.757c-2.764-8.757,2.766-51.021,4.607-55.886C157.936,354.46,156.092,328.002,154.25,321.191z M84.857,234.48c-0.384,6.892-0.635,13.732-0.635,19.683c0,13.621,1.843,31.029,5.529,46.597c0.921,3.892,1.843,14.594,0,20.432c-1.843,6.811-3.686,33.269,1.843,45.917				c1.843,4.864,7.371,47.129,4.607,55.886c-0.921,1.946,0,8.757,0,8.757s-1.843,4.864-5.528,5.837c0,0-2.765,4.865,5.528,3.892				c8.292-0.973,11.978,2.919,13.821,0.974c1.843-1.946,3.686-6.811,1.843-9.729c-1.843-2.919,0.921-5.838,0.921-7.783				c0-1.946-1.843-3.893-2.765-10.703c0-0.973-0.944-41.291,2.742-50.048c1.843-17.364-2.742-28.404-2.742-37.161				c0-8.756,5.529-32.215,5.529-53.619c0-6.662,2.696-16.941,4.052-36.231C114.286,238.093,90.977,236.009,84.857,234.48zM88.01,126.492c0,0,0.01,0.076,0.023,0.171C88.019,126.561,88.01,126.492,88.01,126.492z M87.75,94.917c-0.019-0.057-0.071-0.157-0.149-0.29c1.097-0.092-2.202-0.059-5.199,0.948c-4.025,1.417-8.266,5.413-11.037,12.738c-1.035,2.736-1.51,5.936-1.761,9.639c0,2.919-0.023,6.811-1.866,13.621c-1.843,3.892-5.529,15.567-6.45,24.323c-0.921,3.892-4.607,11.676-5.528,15.567c-0.921,2.919-7.19,29.682-10.778,52.059c0,3.892-7.395,7.783-8.316,9.729s-2.921,7.507-5.186,10.257c-0.319,0.387-0.823,2.312,0.518,2.218c1.122-0.079,3.91-1.945,5.589-3.718c0,0-2.364,11.675-2.364,15.566c0,0,0.521,4.864,2.364,0l2.764-10.702c0,0,0.921-0.973,0,7.783c0,2.919-1.321,10.702,2.364,5.838c0,0,2.243-9.729,2.243-12.648c0,0,0.922-1.945,0.922,2.919c0,0-0.922,11.675,1.843,10.702c0.921-0.973,0.921-9.729,1.843-13.621c0,0,0.921-1.945,0.921,2.919c0,2.919,1.8,12.275,2.764,3.892c-0.079-2.746,1.164-8.423,1.843-25.936c0-2.919,1.25-7.7,4.936-13.538c1.843-2.919,13.136-30.098,13.136-39.827c0-3.892,14.844-39.134,14.844-48.863C86.609,118.001,91.359,105.743,87.75,94.917z M212.27,244.259c-1.764-1.75-5.014-9.061-5.936-11.007s-8.315-5.838-8.315-9.729c-2.255-21.044-8.857-49.14-9.778-52.059c-0.922-3.892-4.607-11.676-5.529-15.567c-0.921-8.756-4.606-20.432-6.449-24.323c-1.843-6.811-1.866-10.702-1.866-13.621c-0.229-3.394-0.608-6.364-1.508-8.942c-2.712-7.77-7.14-11.974-11.29-13.435c-1.994-0.67-3.841-0.987-5.571-1.227c-0.298,0.751-0.671,1.629-0.999,2.498c0.086-0.187-0.565,2.076-0.15,7.18c0.543,6.646,2.213,17.229,1.113,22.466c0,9.729,14.845,44.972,14.845,48.863c0,9.729,11.876,40.158,13.719,43.077c3.686,5.838,3.686,10.702,3.686,13.621				c1.278,17.513,0.588,19.057,1.509,22.603c1.765,7.983,2.765-0.973,2.765-3.892c0-4.864,0.922-2.919,0.922-2.919c0.921,3.892,1.121,12.648,2.042,13.621c2.765,0.973,1.644-10.702,1.644-10.702c0-4.864,0.921-2.919,0.921-2.919c0,2.919,2.243,12.648,2.243,12.648c3.686,4.864,2.364-2.919,2.364-5.838c-0.922-8.756,0-7.783,0-7.783l2.764,10.702				c1.844,4.864,2.164,0,2.164,0c0-3.892-2.164-15.566-2.164-15.566c1.129,1.191,2.521,3.425,3.543,3.851				C211.271,246.824,212.724,244.709,212.27,244.259zM154.877,104.026c-0.415-5.103,0.236-7.366,0.15-7.18c0.328-0.87,0.702-1.748,1-2.499c-2.564-0.354-4.857-0.523-6.89-1.344c-1.339-0.541-2.565-1.072-3.689-1.589c-2.915,2.18-9.106,5.815-23.198,5.335c-12.219-0.417-19.671-3.291-23.582-5.427c-1.649,0.771-3.513,1.578-5.637,2.408c-1.654,0.647-3.465,0.73-5.43,0.896c0.078,0.133,0.13,0.233,0.149,0.29c3.609,10.826-1.141,23.084,0.26,31.575c0,0,2.371,17.832,4.741,28.982c4.221-1.504,10.153-3.523,14.706-4.374c4.68-0.875,9.983-1.627,15.293-1.351c7.18,0.373,13.266,1.431,17.996,2.335c3.427,0.655,7.604,1.335,10.58,1.815c1.916-11.175,4.664-27.408,4.664-27.408C157.09,121.256,155.42,110.673,154.877,104.026zM155.328,193.896c-0.922-6.811-6.666-24.579-4.866-34.979c0.254-1.468,0.549-3.182,0.864-5.017c-2.976-0.48-7.153-1.16-10.58-1.815c-4.73-0.904-10.816-1.962-17.996-2.335c-5.31-0.276-10.613,0.476-15.293,1.351c-10.072,1.882-14.755,4.496-14.724,4.394c-0.013,0.038,0.494,2.179,0.805,3.423c2.6,10.399-3.944,28.168-4.866,34.979c-0.088,0.279-0.201,0.859-0.332,1.688c3.913,1.58,16.325,5.753,36.16,4.666c17.666-0.968,27.332-2.77,31.307-3.685C155.611,195.203,155.447,194.274,155.328,193.896zM134.9,85.032c-0.596-0.533-0.982-4.968-1.021-8.421c-0.864,0.812-1.817,1.55-2.885,2.415				C127.253,82.06,127.768,84,122.099,84c-5.468,0-5.397-1.788-8.783-4.626c-1.129-0.946-2.029-1.742-2.779-2.514				c-0.249,3.256-0.752,7.572-1.437,8.172l-10.432,6.291c3.911,2.136,11.363,5.01,23.582,5.427				c14.092,0.48,20.283-3.155,23.198-5.335C137.705,87.858,134.9,85.032,134.9,85.032z",
      ];

      const option = {
        tooltip: {
          show: true,
          color: "#000",
          formatter: "{b}:{c}%",
        },

        grid: {
          left: "34%",
          top: "0%",
          right: "1%",
          bottom: "8%",
          containLabel: true,
        },
        xAxis: {
          show: false,
        },

        series: [
          {
            type: "graph",
            layout: "force",
            symbolSize: function (size) {
              return size;
            },

            focusNodeAdjacency: true,
            categories: [
              {
                name: "红色",
              },
              {
                name: "黄色",
                itemStyle: {
                  normal: {
                    color: "rgba(255, 255, 0, 0.5)", // 黄色，透明度调整
                  },
                },
              },
              {
                name: "绿色",
                itemStyle: {
                  normal: {
                    color: "rgba(0, 255, 0, 0.5)", // 绿色，透明度调整
                  },
                },
              },
              {
                name: "头部",
                itemStyle: {
                  normal: {
                    color: "rgba(80, 80, 80, 0.6)", // 淡灰色，透明度调整
                  },
                },
              },
              {
                name: "颈部",
                itemStyle: {
                  normal: {
                    color: "rgba(80, 80, 80, 0.5)", // 淡灰色，透明度调整
                  },
                },
              },
              {
                name: "手部",
                itemStyle: {
                  normal: {
                    color: "rgba(80, 80, 80, 0.6)", // 淡灰色，透明度调整
                  },
                },
              },
              {
                name: "胸部",
                itemStyle: {
                  normal: {
                    color: "rgba(140, 140, 140, 0.5)", // 淡灰色，透明度调整
                  },
                },
              },
              {
                name: "腹部",
                itemStyle: {
                  normal: {
                    color: "rgba(120, 120, 120, 0.5)", // 淡灰色，透明度调整
                  },
                },
              },
              {
                name: "盆骨",
                itemStyle: {
                  normal: {
                    color: "rgba(100, 100, 100, 0.5)", // 淡灰色，透明度调整
                  },
                },
              },
              {
                name: "腿",
                itemStyle: {
                  normal: {
                    color: "rgba(100, 100, 100, 0.5)", // 淡灰色，透明度调整
                  },
                },
              },
            ],
            label: {
              normal: {
                show: true,
                textStyle: {
                  fontSize: 14,
                  color: "#000",
                },
              },
            },
            force: {
              repulsion: 550,
            },
            tooltip: {
              formatter: function (node) {
                return node.data.name;
              },
            },
            lineStyle: {
              normal: {
                opacity: 0.3,
                width: 1,
                curveness: 0.5,
              },
            },
            cursor: "pointer",
            data: [
              {
                name: "头部",
                fixed: true,
                draggable: false,
                category: 3,
                number: 0,
                symbol: symbols[0],
                symbolSize: [80, 80],
                value: 10,
                x: 150,
                y: 70,
                itemStyle:{
                  normal:{
                    color:this.highlightedPart==="头部"?"#f25353":"rgba(120, 120, 120, 1.0)"
                  }
                }
              },
              {
                name: "颈部",
                number: 1,
                category: 4,
                fixed: true,
                draggable: false,
                symbol: symbols[1],
                symbolSize: [60, 32],
                value: 20,
                x: 150,
                y: 118,
                itemStyle:{
                  normal:{
                    color:this.highlightedPart==="颈部"?"#f25353":"rgba(120, 120, 120, 0.8)"
                  }
                }
              },
              {
                name: "手部",
                number: 2,
                category: 4,
                fixed: true,
                draggable: false,
                symbol: symbols[2],
                symbolSize: [240, 170],
                value: 25,
                x: 150,
                y: 215,
                itemStyle:{
                  normal:{
                    color:this.highlightedPart==="手部"?"#f25353":"rgba(120, 120, 120, 0.7)"
                  }
                }
              },
              {
                name: "胸部",
                number: 3,
                category: 4,
                fixed: true,
                draggable: false,
                symbol: symbols[3],
                symbolSize: [90, 70],
                value: 25,
                x: 150,
                y: 160,
                itemStyle:{
                  normal:{
                    color:this.highlightedPart==="胸部"?"#f25353":"rgba(120, 120, 120, 0.8)"
                  }
                }
              },
              {
                name: "腹部",
                number: 4,
                category: 4,
                fixed: true,
                draggable: false,
                symbol: symbols[4],
                symbolSize: [90, 50],
                value: 25,
                x: 150,
                y: 215,
                itemStyle:{
                  normal:{
                    color:this.highlightedPart==="腹部"?"#f25353":"rgba(120, 120, 120, 0.9)"
                  }
                }
              },
              {
                name: "盆骨",
                number: 5,
                category: 5,
                fixed: true,
                draggable: false,
                symbol: symbols[5],
                symbolSize: [105, 42],
                value: 25,
                x: 150,
                y: 257,
                itemStyle:{
                  normal:{
                    color:this.highlightedPart==="盆骨"?"#f25353":"rgba(120, 120, 120, 0.8)"
                  }
                }
              },
              {
                name: "腿",
                number: 6,
                category: 6,
                fixed: true,
                draggable: false,
                symbol: symbols[6],
                symbolSize: [100, 220],
                x: 150,
                y: 385,
                itemStyle:{
                  normal:{
                    color:this.highlightedPart==="腿"?"#f25353":"rgba(120, 120, 120, 0.7)"
                  }
                }
              },
              {
                name: "面部",
                number: 7,
                category: 0,
                symbol: "roundRect",
                draggable: true,
                symbolSize: 45,
                value: 1,
              },
              {
                name: "颈前部",
                number: 8,
                category: 1,
                symbol: "roundRect",
                draggable: true,
                symbolSize: 45,
                value: 2,
                itemStyle: {
                  color: "rgba(255, 0, 0, 0.5)", // 红色，透明度调整
                },
              },
              {
                name: "前臂",
                number: 9,
                category: 2,
                symbol: "roundRect",
                draggable: true,
                symbolSize: 45,
                value: 3,
              },
              {
                name: "前胸部",
                number: 10,
                category: 0,
                symbol: "roundRect",
                draggable: true,
                symbolSize: 45,
                value: 1,
              },
              {
                name: "腹腔",
                number: 11,
                category: 1,
                symbol: "roundRect",
                draggable: true,
                symbolSize: 45,
                value: 2,
                itemStyle:{
                  normal:{
                    color:this.highlightedPart==="腹腔"?"#ff0000":"#91c7ae"
                  }
                }
              },
              {
                name: "骨腔",
                number: 12,
                category: 2,
                symbol: "roundRect",
                draggable: true,
                symbolSize: 45,
                value: 3,
              },
              {
                name: "根节点22",
                number: 13,
                category: 0,
                symbol: "roundRect",
                draggable: true,
                symbolSize: 45,
                value: 1,
              },
              {
                name: "颅部",
                number: 14,
                category: 1,
                symbol: "roundRect",
                draggable: true,
                symbolSize: 45,
                value: 2,
              },
              {
                name: "颈后部",
                number: 15,
                category: 2,
                symbol: "roundRect",
                draggable: true,
                symbolSize: 45,
                value: 3,
              },
              {
                name: "上臂",
                number: 16,
                category: 0,
                symbol: "roundRect",
                draggable: true,
                symbolSize: 45,
                value: 1,
              },
              {
                name: "后胸部",
                number: 17,
                category: 1,
                symbol: "roundRect",
                draggable: true,
                symbolSize: 45,
                value: 2,
              },
            ],
            links: [
              {
                source: 0,
                target: 7,
              },
              {
                source: 1,
                target: 8,
              },
              {
                source: 2,
                target: 9,
              },
              {
                source: 3,
                target: 10,
              },
              {
                source: 4,
                target: 11,
              },
              {
                source: 5,
                target: 12,
              },
              {
                source: 6,
                target: 13,
              },
              {
                source: 0,
                target: 14,
              },
              {
                source: 1,
                target: 15,
              },
              {
                source: 2,
                target: 16,
              },
              {
                source: 3,
                target: 17,
              },
            ],
          },
        ],
      };

      this.chart = echarts.init(this.$refs.chart);
      this.chart.setOption(option);

      var i = 0;
      function a() {
        console.log(chart);
        var a = setInterval(function () {
          i++;
          var ss1 =
            chart.getModel()["option"]["series"][0].data[0]["itemStyle"][
              "normal"
            ]["color"];
          var op;
          if (i % 2 === 0) {
            op = option["series"][0].data[0]["itemStyle"]["normal"]["color"] =
              "#91c7ae";
          } else {
            op = option["series"][0].data[0]["itemStyle"]["normal"]["color"] =
              "yellow";
          }
          chart.setOption(option);
        }, 200);
      }
    },
    // 添加新的方法用于更新器官图片路径
    updateOrganImage() {
      
      if (!this.filteredData || !this.filteredData.器官名称) {
        this.organImagePath = "";
        return;
      }

      const organName = this.filteredData.器官名称;
      // 根据器官名称获取对应的图片路径
      const organImageMap = {
        "心": "/images/heart.png",
        "肺": "/images/lung.png",
        "肝胆": "/images/liver.png",
        "肾": "/images/kidney.png",
        "胃": "/images/stomach.png",
        "脑": "/images/brain.png",
        "血管": "/images/blood-vessel.png",

        // 可以根据需要添加更多器官映射
      };

      this.organImagePath = organImageMap[organName] || "/images/default.png";
     
    },
    //添加更新高亮器官部位的方法
    updateHighlightedPart() {
  if (this.filteredData && this.filteredData.器官名称) {
    // 根据器官名称获取对应的身体部位
    this.highlightedPart = this.organToBodyPartMap[this.filteredData.器官名称];
    // 更新图表
    this.updateChart();
  } else {
    this.highlightedPart = null;
    this.updateChart();
  }
},
  },

  watch: {
    selectedData: "updateChart",
  },
  mounted() {
    // 删除 this.ilness() 调用
    this.drawHeatmap();
    this.updateChart();
  },
};
</script>
<style scoped>
/*ID输入框 */
.search-box {
  margin-top: 5px;

  margin-right: auto;

  justify-content: center;
  align-items: center;
  width: 250px;
}

.search-box input[type="text"] {
  width: 100%;
  padding: 10px;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.search-box input[type="text"]:focus {
  outline: none;
  border-color: #007bff;
}
/* 信息展示框 */
.info-box {
  background: linear-gradient(
    150deg,
    rgba(210, 217, 237, 0.8),
    rgba(191, 198, 220, 0.8)
  );
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  display: inline-block;
  color: #333;
}

.info-box-content {
  max-width: 800px;
  margin: auto;
  text-align: center;
}

.info-box-content h2 {
  font-size: 24px;
  margin-bottom: 10px;
  color: #333;
}

.info-box-content p {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 15px;
  color: #555;
}

.el-card {
  background: linear-gradient(
    150deg,
    rgba(210, 217, 237, 0),
    rgba(191, 198, 220, 0.1)
  );
}
.graph {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  .el-card {
    width: 49%;
  }
}
.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: "Arial", sans-serif;
  color: #333;
}

.search-container {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.search-container label {
  margin-right: 10px;
}
.inputBx input {
  /* border: 2px solid #fff;
	border-radius: 40px;
	font-size: 1.2em;
	color: #fff;
	box-shadow: none; */
  border: 2px solid #fff;
  color: #fff;
  border-radius: 40px;
  box-shadow: none;
  outline: none;
}
.input-text {
  flex-grow: 1;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-right: 10px;
}

.search-button {
  background-color: #99b7d7;
  color: white;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.search-button:hover {
  background-color: #606f80;
}

.result {
  margin-top: 20px;
  border: 1px solid #ccc;
  padding: 20px;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.item {
  margin-top: 10px;
  padding: 10px;
  border-left: 3px solid #8198b1;
  background-color: #f7f7f7;
}

.item-title {
  font-size: 1.25em;
  margin-bottom: 5px;
}

.item-info {
  margin-bottom: 5px;
}

.details {
  margin-top: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  border-top: 1px solid #ddd;
}

.details-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.symptom {
  margin-bottom: 5px;
}

.details-button {
  padding: 5px 10px;
  background-color: #9eafbf;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.details-button:hover {
  background-color: #5a6268;
}
.a {
  text-align: center;
  background: linear-gradient(
    150deg,
    rgba(210, 217, 237, 0.8),
    rgba(191, 198, 220, 0.8)
  );
}
.b {
  text-align: center;
  background: linear-gradient(
    150deg,
    rgba(210, 217, 237, 0.8),
    rgba(191, 198, 220, 0.8)
  );
}
.a span {
  background-color: #5879e7;
  border-radius: 10px;
  width: 20px;
  color: #fffbfb;
  font-size: 15px;
  border: 2px solid #fff;
}
.b span {
  background-color: #ee974b;
  border-radius: 10px;
  width: 20px;
  color: #fff;
  font-size: 15px;
  border: 2px solid #ffffff;
}

/* 信息展示框配置 */
.search-bar {
  margin-left: auto;
  margin-right: auto;
}
.search-bar input {
  margin-top: 10px;
  width: 85%;
  padding: 10px;
  border: 2px solid #ddd;

  border: none;
  border-radius: 5px;
  margin-bottom: 20px;
}
.search-bar input[type="text"] {
  width: 80%;
  padding: 10px;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 15px;
  transition: all 0.3s ease;
}

.search-bar input[type="text"]:focus {
  outline: none;
  border-color: #007bff;
}
.card {
  background-image: linear-gradient(
    to bottom,
    rgba(157, 134, 175, 0.8),
    rgba(104, 95, 127, 0.8)
  );
  color: white;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  margin: 20px auto;
}

.modal {
  display: block;
  position: fixed;
  z-index: 1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-image: linear-gradient(
    to right,
    rgba(173, 140, 198, 0.5),
    rgba(111, 103, 133, 0.5)
  );
  margin: 15% auto;
  padding: 20px;
  width: 660px; /* Width of the modal */
  border-radius: 15px; /* Rounded corners */
  position: relative;
}

.close {
  color: white;
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.modal-body {
  height: 550px; /* Set height for the modal body */
  width: 650px;
  /* overflow-y: auto;  */
}

.check-details {
  height: 500px; /* Limit height of check details */
  width: 630px;
}

/* ... 其他样式保持不变 ... */

.organ-display {
  height: 250px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 10px;
}

.organ-image {
  height: 200px;
  width: 180px;
  object-fit: contain; /* 保持图片比例 */
  margin-bottom: 10px;
  transition: transform 0.3s ease;
}

.organ-image:hover {
  transform: scale(1.05);
}

.organ-name {
  margin-top: 5px;
  font-size: 16px;
  color: #606266;
  text-align: center;
}

.no-data-tip {
  height: 250px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
  font-size: 14px;
}
</style>

