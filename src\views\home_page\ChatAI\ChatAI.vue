<template>
  <div class="chat-container">
    <div class="particle-container">
      <ParticleBackground />
    </div>
    <div class="sidebar">
      <div class="sidebar-header">
        <h2>AI 助手</h2>
      </div>
      <div class="sidebar-buttons">
        <button
          :class="{ active: domain === 'generalv3' }"
          @click="selectAI('generalv3.5', 'v3.5/chat')"
        >
          <i class="el-icon-chat-dot-round"></i>
          通用问答AI
        </button>
        <button
          :class="{ active: domain === 'lite' }"
          @click="selectAI('lite', 'v1.1/chat')"
        >
          <i class="el-icon-first-aid-kit"></i>
          模拟医生AI
        </button>
        <button
          :class="{ active: domain === 'generalv3_5' }"
          @click="selectAI('generalv3_5', 'v3.5/chat')"
        >
          <i class="el-icon-data-analysis"></i>
          医疗数据分析AI
        </button>
      </div>
    </div>
    <div class="main-content">
      <div class="chat-window">
        <div class="ai-intro" v-if="currentAI && !sparkResult">
          <div class="ai-avatar">
            <img :src="currentAI.avatar" :alt="currentAI.name" />
          </div>
          <h3>{{ currentAI.name }}</h3>
          <p class="ai-title">{{ currentAI.title }}</p>
          <div class="ai-description">
            <h4>专业背景：</h4>
            <p>{{ currentAI.background }}</p>
            <h4>擅长领域：</h4>
            <ul>
              <li v-for="(skill, index) in currentAI.skills" :key="index">
                {{ skill }}
              </li>
            </ul>
          </div>
        </div>
        <div id="results" v-html="sparkResult" ref="results"></div>
      </div>
      <div class="action-bar">
        <button id="downloadBtn" @click="downloadAIResponse">
          <i class="el-icon-download"></i>
          下载对话记录
        </button>
      </div>
      <div class="input-area">
        <input
          id="question"
          type="text"
          v-model="question"
          @keydown.enter="sendMsg"
          :placeholder="currentAI ? currentAI.placeholder : '请先选择AI助手...'"
        />
        <button id="sendBtn" @click="sendMsg">
          <i class="el-icon-s-promotion"></i>
          发送
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import * as base64 from "base-64";
import CryptoJs from "crypto-js";

export default {
  // 父组件传值(传给体检解读)
  props: {
    inspectionContent: {
      type: String,
      default: "",
    },
    initialDomain: {
      type: String,
      default: "generalv3.5",
    },
    initialVersion: {
      type: String,
      default: "v3.5/chat",
    },
  },
  data() {
    return {
      a: 1,
      question: "",
      sparkResult: "",
      domain: "generalv3.5",
      version: "v3.5/chat",
      requestObj: {
        APPID: "0f4dc878",
        APISecret: "M2Q1NjkzZmM4ZDUzMmQxMTc0NzI5OWU2",
        APIKey: "934d2502b887854f8f0b375c1d27a17c",
        Uid: "润",
      },
      aiProfiles: {
        generalv3: {
          name: "通用问答AI",
          title: "全能知识顾问",
          avatar:
            "https://docs.dknowc.cn/20220424/1531/41fbc4a5-2ebc-488f-8427-e205c7d19960/%E5%BD%A9%E6%99%BA%E6%9C%BA%E5%99%A8%E4%BA%BA.png",
          background:
            "拥有海量知识库，经过大规模预训练，能够理解和回答各类问题。",
          skills: [
            "科学、历史、文化等知识解答",
            "数理化等学科问题指导",
            "生活、工作等实用建议",
            "文本理解与分析",
          ],
          placeholder: "您可以问我任何问题...",
          intro:
            "您好！我是全能AI助手，可以为您解答各类问题，提供准确信息和建议。让我们开始对话吧！",
        },
        lite: {
          name: "模拟医生AI",
          title: "主任医师",
          avatar:
            "https://imgpp.ztupic.com/dealupload/dealyisheng202401200110.png?x-oss-process=image/resize,w_800/quality,q_85/sharpen,110/watermark,image_d2F0ZXIxMjExMDIucG5n,t_90,g_se,x_0,y_100/watermark,type_ZmFuZ3poZW5naGVpdGk,text_5L2c5ZOB57yW5Y%2B3OjcwMDE4NzA%3D,interval_530,align_1,t_100,g_se,x_75,y_118,color_FFFFFF,size_15",
          background:
            "具有丰富的临床经验，专注于疾病诊断和健康咨询，熟悉各类疾病的诊断和治疗方案。",
          skills: [
            "症状分析与初步诊断",
            "疾病预防与保健建议",
            "用药指导与注意事项",
            "急救知识与健康教育",
          ],
          placeholder: "请描述您的症状或健康问题...",
          intro:
            "您好！我是主任医师AI，有着丰富的临床经验。请详细描述您的症状，我会认真为您解答。注意：我的建议仅供参考，重要医疗决策请务必咨询真实医生。",
        },
        generalv3_5: {
          name: "医疗数据分析AI",
          title: "医学数据专家",
          avatar:
            "https://ts1.cn.mm.bing.net/th/id/R-C.********************************?rik=2o13QHbkhDhEiQ&riu=http%3a%2f%2fwww.greenheal.com.cn%2fimg%2fzhihui.jpg&ehk=7iPoKULRykM7GfIeyFtORDNxsx9i8EjvgyRUITZyLPM%3d&risl=&pid=ImgRaw&r=0",
          background:
            "专门从事医疗数据分析和健康报告解读，具有专业的医学知识和数据分析能力。",
          skills: [
            "体检报告解读与分析",
            "健康指标评估",
            "疾病风险预测",
            "健康趋势分析",
          ],
          placeholder: "请输入您想分析的医疗数据或报告...",
          intro:
            "您好！我是医疗数据分析专家，专注于医疗报告解读和健康数据分析。我可以帮您理解检验结果，评估健康状况。",
        },
      },
      currentAI: null,
    };
  },
  //AI请求地址

  watch: {
    inspectionContent: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.question = newVal;
          this.sendMsg();
          this.a = 0;
        }
      },
    },
  },
  created() {
    this.selectAI(this.initialDomain, this.initialVersion);
  },
  methods: {
    selectAI(domain, version) {
      this.domain = domain;
      this.version = version;
      this.currentAI = this.aiProfiles[domain];
      this.sparkResult = ""; // 清空对话内容，显示AI介绍
    },
    async sendMsg() {
      const myUrl = await this.getWebsocketUrl();
      const inputVal = this.question;
      const socket = new WebSocket(myUrl);

      socket.addEventListener("open", (event) => {
        console.log("开启连接！！", event);
        const params = {
          header: {
            app_id: this.requestObj.APPID,
            uid: this.requestObj.Uid,
          },
          parameter: {
            chat: {
              domain: this.domain,
              temperature: 0.5,
              max_tokens: 1024,
            },
          },
          payload: {
            message: {
              text: [{ role: "user", content: inputVal }],
            },
          },
        };
        console.log("发送消息");
        if (this.a === 1) {
          this.sparkResult += `<p class="user-message">${inputVal}</p>`;
        } // 添加用户的信息
        socket.send(JSON.stringify(params));
      });

      socket.addEventListener("message", (event) => {
        const data = JSON.parse(event.data);
        if (data.payload.choices.text && data.payload.choices.text.length > 0) {
          const response = data.payload.choices.text[0].content;
          this.sparkResult += response;
          // 触发事件通知父组件
          this.$emit("ai-response", this.sparkResult);
          this.$emit("ai-other-response", this.sparkResult);
        }
        if (data.header.code !== 0) {
          console.log("出错了", data.header.code, ":", data.header.message);
          socket.close();
        }
        if (
          data.header.code === 0 &&
          data.payload.choices.text &&
          data.header.status === 2
        ) {
          this.sparkResult += data.payload.choices.text[0].content;
          setTimeout(() => {
            socket.close();
          }, 1000);
        }
      });

      socket.addEventListener("close", (event) => {
        console.log("连接关闭！！", event);
        this.sparkResult += "\n";
        this.question = ""; // 清空输入框
      });

      socket.addEventListener("error", (event) => {
        console.log("连接发送错误！！", event);
      });
    },
    getWebsocketUrl() {
      return new Promise((resolve, reject) => {
        const url = "wss://spark-api.xf-yun.com/" + this.version;
        const host = "spark-api.xf-yun.com";
        const apiKeyName = "api_key";
        const date = new Date().toGMTString();
        const algorithm = "hmac-sha256";
        const headers = "host date request-line";
        const signatureOrigin = `host: ${host}\ndate: ${date}\nGET /${this.version} HTTP/1.1`;
        const signatureSha = CryptoJs.HmacSHA256(
          signatureOrigin,
          this.requestObj.APISecret
        );
        const signature = CryptoJs.enc.Base64.stringify(signatureSha);
        const authorizationOrigin = `${apiKeyName}="${this.requestObj.APIKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
        const authorization = base64.encode(authorizationOrigin);
        const finalUrl = `${url}?authorization=${authorization}&date=${encodeURI(
          date
        )}&host=${host}`;
        resolve(finalUrl);
      });
    },
    downloadAIResponse() {
      const element = document.createElement("a");
      element.setAttribute(
        "href",
        "data:text/plain;charset=utf-8," + encodeURIComponent(this.sparkResult)
      );
      element.setAttribute("download", "ai-response.txt");
      element.style.display = "none";
      document.body.appendChild(element);
      element.click();
      document.body.removeChild(element);
    },
  },
};
</script>

<style>
.chat-container {
  position: relative;
  z-index: 1;
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);

}

.particle-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.sidebar {
  position: relative;
  z-index: 1;
  width: 260px;
  background: linear-gradient(180deg, #2c3e50 0%, #3498db 100%);
  padding: 20px;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.sidebar-header {
  margin-bottom: 30px;
  text-align: center;
}

.sidebar-header h2 {
  color: #fff;
  font-size: 24px;
  margin: 0;
  padding: 10px 0;
}

.sidebar-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.sidebar button {
  padding: 15px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
}

.sidebar button i {
  font-size: 18px;
}

.sidebar button:hover,
.sidebar button.active {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.main-content {
  position: relative;
  z-index: 1;
  flex: 1;
  padding: 30px;
  display: flex;
  flex-direction: column;
}

.chat-window {
  flex: 1;
  background:#fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow-y: auto;
  backdrop-filter: blur(5px);
}

#results {
  font-size: 16px;
  line-height: 1.6;
  color: #2c3e50;
}

#results .user-message {
  background: #e3f2fd;
  color: #1565c0;
  padding: 12px 16px;
  border-radius: 12px;
  margin: 10px 0;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.1);
  max-width: 80%;
  margin-left: auto;
  word-wrap: break-word;
  backdrop-filter: blur(5px);
}

#results .ai-message {
  background: #f8f9fa;
  color: #3a7bd5;
  padding: 12px 16px;
  border-radius: 12px;
  margin: 10px 0;
  border-left: 4px solid #3a7bd5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  max-width: 80%;
  margin-right: auto;
  word-wrap: break-word;
  backdrop-filter: blur(5px);
}

#results pre {
  background: #2c3e50;
  color: #e3f2fd;
  padding: 12px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 10px 0;
}

#results a {
  color: #2196f3;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.3s ease;
}

#results a:hover {
  border-bottom-color: #2196f3;
}

#results strong {
  color: #1976d2;
  font-weight: 600;
}

#results blockquote {
  border-left: 4px solid #90caf9;
  margin: 10px 0;
  padding: 10px 20px;
  background: rgba(33, 150, 243, 0.05);
  color: #546e7a;
}

#results ul,
#results ol {
  padding-left: 20px;
  color: #2c3e50;
}

#results li {
  margin: 5px 0;
}

#results hr {
  border: none;
  height: 1px;
  background: linear-gradient(to right, transparent, #90caf9, transparent);
  margin: 20px 0;
}

.action-bar {
  margin-bottom: 20px;
}

#downloadBtn {
  background: #2196f3;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

#downloadBtn:hover {
  background: #1976d2;
  transform: translateY(-2px);
}

.input-area {
  position: relative;
  z-index: 3;
  display: flex;
  gap: 15px;
}

.input-area input {
  flex: 1;
  padding: 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.input-area input:focus {
  border-color: #2196f3;
  outline: none;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

#sendBtn {
  background: #4caf50;
  color: white;
  border: none;
  padding: 0 30px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
}

#sendBtn:hover {
  background: #43a047;
  transform: translateY(-2px);
}

/* 添加响应式设计 */
@media (max-width: 768px) {
  .chat-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
  }

  .sidebar-buttons {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }

  .sidebar button {
    flex: 1;
    min-width: 150px;
  }
}

.particle-background {
  position: fixed !important;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

/* 添加AI介绍卡片样式 */
.ai-intro {
  text-align: center;
  padding: 30px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin: 20px;
  backdrop-filter: blur(10px);
}

.ai-avatar {
  width: 100px;
  height: 100px;
  margin: 0 auto 20px;
}

.ai-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #2196f3;
}

.ai-intro h3 {
  color: #2c3e50;
  font-size: 24px;
  margin: 0 0 5px;
}

.ai-title {
  color: #2196f3;
  font-size: 18px;
  margin: 0 0 20px;
}

.ai-description {
  text-align: left;
  color: #34495e;
}

.ai-description h4 {
  color: #2c3e50;
  margin: 15px 0 10px;
}

.ai-description ul {
  list-style-type: none;
  padding: 0;
}

.ai-description li {
  padding: 5px 0;
  padding-left: 20px;
  position: relative;
}

.ai-description li:before {
  content: "•";
  color: #2196f3;
  position: absolute;
  left: 0;
}

/* 修改输入框禁用状态样式 */
.input-area input:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}
</style>
